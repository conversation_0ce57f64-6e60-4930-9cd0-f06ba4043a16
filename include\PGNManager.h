#pragma once

#include "ChessTypes.h"
#include "Move.h"
#include "ChessGame.h"
#include <string>
#include <vector>
#include <map>

// PGN (Portable Game Notation) Manager
class PGNManager {
private:
    std::map<std::string, std::string> headers_;
    std::vector<std::string> moveList_;
    std::string result_;
    
public:
    PGNManager();
    
    // Header management
    void setHeader(const std::string& key, const std::string& value);
    std::string getHeader(const std::string& key) const;
    void setStandardHeaders(const std::string& white, const std::string& black, 
                           const std::string& event = "Casual Game", 
                           const std::string& site = "Local");
    
    // Game recording
    void addMove(const Move& move, const ChessGame& game);
    void addMove(const std::string& algebraicMove);
    void setResult(const std::string& result);
    
    // PGN generation
    std::string generatePGN() const;
    std::string getMovesString() const;
    
    // File operations
    bool saveToFile(const std::string& filename) const;
    bool loadFromFile(const std::string& filename);
    
    // Game replay
    std::vector<Move> parseMoves(const std::string& moveText) const;
    bool replayGame(ChessGame& game) const;
    
    // Utility
    void clear();
    int getMoveCount() const { return moveList_.size(); }
    
private:
    std::string getCurrentDate() const;
    std::string moveToAlgebraic(const Move& move, const ChessGame& game) const;
    std::string formatMoveNumber(int moveNumber, bool isWhite) const;
};

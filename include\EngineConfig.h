#pragma once

#include <string>
#include <unordered_map>
#include <vector>
#include <functional>
#include <memory>

// Struktur untuk menyimpan informasi parameter konfigurasi
struct ConfigParameter {
    enum Type {
        SPIN,    // Integer dengan min/max
        CHECK,   // Boolean
        COMBO,   // <PERSON><PERSON><PERSON> dari list
        STRING,  // String bebas
        BUTTON   // Tombol aksi
    };
    
    Type type;
    std::string name;
    std::string description;
    std::string defaultValue;
    std::string currentValue;
    
    // Untuk SPIN
    int minValue = 0;
    int maxValue = 0;
    
    // Untuk COMBO
    std::vector<std::string> options;
    
    // Callback untuk validasi dan aksi
    std::function<bool(const std::string&)> validator;
    std::function<void(const std::string&)> onChange;
    
    ConfigParameter() = default;
    ConfigParameter(Type t, const std::string& n, const std::string& desc, 
                   const std::string& def) 
        : type(t), name(n), description(desc), defaultValue(def), currentValue(def) {}
};

// Class untuk mengelola konfigurasi engine
class EngineConfig {
private:
    std::unordered_map<std::string, std::unique_ptr<ConfigParameter>> parameters_;
    std::string configFilePath_;
    
    // Helper functions
    std::string toLowerCase(const std::string& str) const;
    bool validateSpinValue(const std::string& value, int min, int max) const;
    bool validateBoolValue(const std::string& value) const;
    bool validateComboValue(const std::string& value, const std::vector<std::string>& options) const;
    
public:
    EngineConfig();
    ~EngineConfig() = default;
    
    // Inisialisasi parameter default
    void initializeDefaultParameters();
    
    // Parameter management
    bool addParameter(const ConfigParameter& param);
    bool setParameter(const std::string& name, const std::string& value);
    std::string getParameter(const std::string& name) const;
    bool hasParameter(const std::string& name) const;
    
    // Specialized getters
    int getIntParameter(const std::string& name) const;
    bool getBoolParameter(const std::string& name) const;
    std::string getStringParameter(const std::string& name) const;
    
    // Parameter info
    const ConfigParameter* getParameterInfo(const std::string& name) const;
    std::vector<std::string> getAllParameterNames() const;
    
    // File operations
    bool loadFromFile(const std::string& filename);
    bool saveToFile(const std::string& filename) const;
    
    // Reset to defaults
    void resetToDefaults();
    void resetParameter(const std::string& name);
    
    // Display functions
    std::string getParameterDisplayString(const std::string& name) const;
    std::string getAllParametersString() const;
    
    // UCI compatibility
    std::string getUCIOptionsString() const;
    bool setUCIOption(const std::string& name, const std::string& value);
};

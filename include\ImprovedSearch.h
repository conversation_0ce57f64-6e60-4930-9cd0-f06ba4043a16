#pragma once

#include "ChessTypes.h"
#include "Move.h"
#include "ChessGame.h"
#include <vector>
#include <algorithm>

class ImprovedSearch {
public:
    // Late Move Reduction (LMR) parameters
    static const int LMR_DEPTH_THRESHOLD = 3;
    static const int LMR_MOVE_THRESHOLD = 4;
    static const int LMR_MAX_REDUCTION = 3;
    
    // Futility pruning margins
    static const int FUTILITY_MARGIN_1 = 200;
    static const int FUTILITY_MARGIN_2 = 300;
    static const int FUTILITY_MARGIN_3 = 500;
    
    // Null move parameters
    static const int NULL_MOVE_DEPTH_THRESHOLD = 2;
    static const int NULL_MOVE_REDUCTION = 2;
    
    // Aspiration window parameters
    static const int ASPIRATION_WINDOW = 50;
    static const int MAX_ASPIRATION_WINDOW = 400;
    
    // Calculate LMR reduction
    static int calculateLMRReduction(int depth, int moveNumber, bool isPVNode, bool isCapture, 
                                   bool isCheck, bool isKiller) {
        if (depth < LMR_DEPTH_THRESHOLD || moveNumber < LMR_MOVE_THRESHOLD) {
            return 0;
        }
        
        if (isPVNode || isCapture || isCheck || isKiller) {
            return 0;
        }
        
        // Base reduction
        int reduction = 1;
        
        // Increase reduction for later moves
        if (moveNumber > 8) {
            reduction++;
        }
        if (moveNumber > 16) {
            reduction++;
        }
        
        // Increase reduction for deeper searches
        if (depth > 6) {
            reduction++;
        }
        
        return std::min(reduction, LMR_MAX_REDUCTION);
    }
    
    // Futility pruning check
    static bool canPruneFutility(int depth, int alpha, int staticEval, bool inCheck) {
        if (inCheck || depth > 3) {
            return false;
        }
        
        int margin;
        switch (depth) {
            case 1: margin = FUTILITY_MARGIN_1; break;
            case 2: margin = FUTILITY_MARGIN_2; break;
            case 3: margin = FUTILITY_MARGIN_3; break;
            default: return false;
        }
        
        return staticEval + margin <= alpha;
    }
    
    // Reverse futility pruning (static null move pruning)
    static bool canPruneReverseFutility(int depth, int beta, int staticEval, bool inCheck) {
        if (inCheck || depth > 3) {
            return false;
        }
        
        int margin;
        switch (depth) {
            case 1: margin = FUTILITY_MARGIN_1; break;
            case 2: margin = FUTILITY_MARGIN_2; break;
            case 3: margin = FUTILITY_MARGIN_3; break;
            default: return false;
        }
        
        return staticEval - margin >= beta;
    }
    
    // Null move pruning check
    static bool canDoNullMove(const ChessBoard& board, Color color, bool inCheck, int depth) {
        if (inCheck || depth < NULL_MOVE_DEPTH_THRESHOLD) {
            return false;
        }
        
        // Don't do null move in endgame with only pawns (zugzwang risk)
        int pieceCount = 0;
        bool hasNonPawnPieces = false;
        
        for (int rank = 0; rank < 8; ++rank) {
            for (int file = 0; file < 8; ++file) {
                const Piece* piece = board.getPiece(Position(rank, file));
                if (piece && piece->getColor() == color) {
                    pieceCount++;
                    if (piece->getType() != PieceType::PAWN && piece->getType() != PieceType::KING) {
                        hasNonPawnPieces = true;
                    }
                }
            }
        }
        
        return hasNonPawnPieces && pieceCount > 3;
    }
    
    // Calculate null move reduction
    static int calculateNullMoveReduction(int depth, int staticEval, int beta) {
        int reduction = NULL_MOVE_REDUCTION;
        
        // Adaptive null move reduction
        if (staticEval - beta > 200) {
            reduction++;
        }
        if (depth > 6) {
            reduction++;
        }
        
        return reduction;
    }
    
    // Razoring check
    static bool canRazor(int depth, int alpha, int staticEval) {
        if (depth != 1) {
            return false;
        }
        
        return staticEval + FUTILITY_MARGIN_1 <= alpha;
    }
    
    // Internal Iterative Deepening check
    static bool shouldDoIID(int depth, bool isPVNode, const Move& hashMove) {
        return isPVNode && depth >= 4 && !hashMove.isValid();
    }
    
    // Multi-Cut pruning
    static bool canMultiCut(int depth, int cutoffCount) {
        return depth >= 3 && cutoffCount >= 3;
    }
    
    // Probcut pruning
    static bool canProbCut(int depth, int beta, int staticEval) {
        if (depth < 5) {
            return false;
        }
        
        int probCutBeta = beta + 200;
        return staticEval >= probCutBeta;
    }
    
    // Check extension
    static int getCheckExtension(bool inCheck) {
        return inCheck ? 1 : 0;
    }
    
    // Pawn to 7th rank extension
    static int getPawnExtension(const Move& move, const ChessBoard& board) {
        const Piece* piece = board.getPiece(move.getFrom());
        if (!piece || piece->getType() != PieceType::PAWN) {
            return 0;
        }
        
        int targetRank = move.getTo().rank;
        if ((piece->getColor() == Color::WHITE && targetRank == 6) ||
            (piece->getColor() == Color::BLACK && targetRank == 1)) {
            return 1;
        }
        
        return 0;
    }
    
    // Recapture extension
    static int getRecaptureExtension(const Move& move, const Move& previousMove, const ChessBoard& board) {
        if (!previousMove.isValid()) {
            return 0;
        }
        
        // Check if this move recaptures on the same square
        if (move.getTo() == previousMove.getTo()) {
            const Piece* capturedPiece = board.getPiece(move.getTo());
            if (capturedPiece) {
                return 1;
            }
        }
        
        return 0;
    }
    
    // Calculate total extensions
    static int calculateExtensions(const Move& move, const ChessBoard& board, bool inCheck, 
                                 const Move& previousMove) {
        int extensions = 0;
        
        extensions += getCheckExtension(inCheck);
        extensions += getPawnExtension(move, board);
        extensions += getRecaptureExtension(move, previousMove, board);
        
        // Limit total extensions
        return std::min(extensions, 2);
    }
    
    // Aspiration window search
    static std::pair<int, int> getAspirationWindow(int previousScore, int iteration) {
        if (iteration <= 1) {
            return {std::numeric_limits<int>::min(), std::numeric_limits<int>::max()};
        }
        
        int window = ASPIRATION_WINDOW;
        
        // Widen window for later iterations
        if (iteration > 3) {
            window *= 2;
        }
        if (iteration > 5) {
            window *= 2;
        }
        
        window = std::min(window, MAX_ASPIRATION_WINDOW);
        
        return {previousScore - window, previousScore + window};
    }
    
    // Check if we should research with wider window
    static bool shouldResearchAspirationWindow(int score, int alpha, int beta) {
        return score <= alpha || score >= beta;
    }
    
    // Singular extension check
    static bool canSingularExtend(int depth, int ttScore, int beta, int alpha) {
        return depth >= 6 && ttScore < beta && ttScore > alpha;
    }
    
    // Delta pruning for quiescence search
    static bool canDeltaPrune(int alpha, int staticEval, const Move& move, const ChessBoard& board) {
        const Piece* capturedPiece = board.getPiece(move.getTo());
        if (!capturedPiece) {
            return false;
        }
        
        int delta = capturedPiece->getValue() + 200; // Safety margin
        return staticEval + delta < alpha;
    }
    
    // SEE (Static Exchange Evaluation) pruning
    static bool canSEEPrune(const Move& move, const ChessBoard& board, int threshold = 0) {
        // Simplified SEE - in real implementation, calculate full exchange sequence
        const Piece* attacker = board.getPiece(move.getFrom());
        const Piece* victim = board.getPiece(move.getTo());
        
        if (!attacker || !victim) {
            return false;
        }
        
        int gain = victim->getValue() - attacker->getValue();
        return gain < threshold;
    }
};

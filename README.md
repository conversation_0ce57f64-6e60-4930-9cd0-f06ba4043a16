# Chess Engine

A complete chess engine implementation in C++ featuring a console-based interface and AI opponent using minimax algorithm with alpha-beta pruning.

## Features

### Core Chess Functionality
- **Complete rule implementation**: All standard chess rules including castling, en passant, and pawn promotion
- **Move validation**: Comprehensive move validation system ensuring only legal moves are allowed
- **Game state detection**: Automatic detection of check, checkmate, stalemate, and draw conditions
- **Move history**: Complete game history tracking with algebraic notation

### AI Engine
- **Minimax algorithm**: Advanced AI using minimax with alpha-beta pruning for efficient search
- **Position evaluation**: Sophisticated evaluation function considering:
  - Material values (piece worth)
  - Piece-square tables for positional play
  - Endgame vs middlegame evaluation
  - King safety and piece activity
- **Configurable depth**: Adjustable search depth (default: 4 moves ahead)
- **Move ordering**: Optimized move ordering for better alpha-beta pruning efficiency

### User Interface
- **Console-based interface**: Clean, intuitive text-based interface
- **Algebraic notation**: Standard chess notation for move input (e.g., "e2e4")
- **Visual board display**: ASCII art board representation with piece symbols
- **Interactive commands**: Help system, board display, game status, and more
- **Error handling**: Comprehensive input validation and error messages

### Engine Configuration System
- **NUMA Offset**: Memory allocation optimization for multi-processor systems (0-7)
- **Threads**: Configurable parallel search threads (1-128)
- **Hash Table**: Adjustable hash table size (1-32768 MB)
- **Table Memory**: Tablebase cache configuration (1-8192 MB)
- **MultiPV**: Multiple principal variations display (1-500)
- **Opening Book**: Configurable book file and move limits
- **Syzygy Tablebase**: Path and probe depth configuration
- **Time Management**: Overhead and usage percentage settings
- **Logging**: Configurable log file output
- **Interactive Configuration Tool**: User-friendly interface for all settings
- **UCI Integration**: Full UCI protocol support with all parameters

### Advanced Features
- **Opening book support**: Built-in opening book with configurable moves
- **Endgame tablebase integration**: Syzygy tablebase support for perfect endgame play
- **PGN file support**: Game saving/loading in standard PGN format
- **UCI protocol**: Full Universal Chess Interface support for GUI compatibility
- **Advanced search techniques**:
  - Transposition tables with configurable hash size
  - Killer move heuristic
  - History heuristic
  - Principal variation search
  - Null move pruning
  - Late move reduction (LMR)
  - Multi-cut pruning
  - Futility pruning

## Project Structure

```
Chess-Engine/
├── include/                # Header files
│   ├── ChessTypes.h        # Basic types and enums
│   ├── Piece.h             # Base piece class
│   ├── Pieces.h            # Specific piece implementations
│   ├── ChessBoard.h        # Board representation
│   ├── Move.h              # Move class
│   ├── ChessGame.h         # Game logic
│   ├── ChessEngine.h       # AI engine
│   ├── ChessUI.h           # User interface
│   ├── UCIProtocol.h       # UCI protocol implementation
│   ├── EngineConfig.h      # Engine configuration system
│   ├── EngineConfigUI.h    # Configuration interface
│   ├── OpeningBook.h       # Opening book support
│   ├── EndgameTablebase.h  # Tablebase support
│   └── PGNManager.h        # PGN file handling
├── src/                    # Implementation files
│   ├── Piece.cpp
│   ├── Pieces.cpp
│   ├── ChessBoard.cpp
│   ├── ChessGame.cpp
│   ├── ChessEngine.cpp
│   ├── ChessUI.cpp
│   ├── UCIProtocol.cpp
│   ├── EngineConfig.cpp
│   ├── EngineConfigUI.cpp
│   ├── OpeningBook.cpp
│   ├── EndgameTablebase.cpp
│   └── PGNManager.cpp
├── main.cpp                # Main console application
├── uci_main.cpp            # UCI protocol main
├── config_main.cpp         # Configuration tool main
├── Makefile                # Build configuration
├── ENGINE_CONFIG_README.md # Configuration documentation
└── README.md               # This file
```

## Building the Project

### Prerequisites
- C++17 compatible compiler (GCC 7+, Clang 5+, MSVC 2017+)
- Make (optional, for using Makefile)

### Build Instructions

#### Quick Build (Recommended)
```bash
# Build UCI engine for GUI compatibility
g++ -std=c++17 -Iinclude -O2 uci_main.cpp src/*.cpp -o chess_engine_uci

# Build console version
g++ -std=c++17 -Iinclude -O2 main.cpp src/*.cpp -o chess_engine

# Build configuration tool
g++ -std=c++17 -Iinclude -O2 config_main.cpp src/EngineConfig.cpp src/EngineConfigUI.cpp -o engine_config
```

#### Using Makefile
```bash
# Build all targets
make all

# Build specific targets
make chess_engine      # Console version
make chess_engine_uci  # UCI version
make engine_config     # Configuration tool

# Clean build files
make clean
```

#### Manual Compilation Examples
```bash
# UCI engine (for chess GUIs)
g++ -std=c++17 -Iinclude -O2 uci_main.cpp src/*.cpp -o chess_engine_uci

# Console game
g++ -std=c++17 -Iinclude -O2 main.cpp src/*.cpp -o chess_engine

# Configuration tool
g++ -std=c++17 -Iinclude -O2 config_main.cpp src/EngineConfig.cpp src/EngineConfigUI.cpp -o engine_config
```

## How to Use

### Using with Chess GUIs (Recommended)

The engine supports UCI protocol and works with popular chess GUIs:

#### n Croissant GUI
1. Build the UCI engine: `g++ -std=c++17 -Iinclude -O2 uci_main.cpp src/*.cpp -o chess_engine_uci`
2. Open n Croissant
3. Go to Engine settings
4. Add new engine and point to `chess_engine_uci.exe`
5. Configure engine parameters through the GUI

#### Other UCI-compatible GUIs
- Arena Chess GUI
- ChessBase
- Scid vs. PC
- Cute Chess

### Engine Configuration

#### Using Configuration Tool
```bash
# Run interactive configuration
./engine_config

# Command line options
./engine_config --show          # Show current settings
./engine_config --save config.ini  # Save configuration
./engine_config --load config.ini  # Load configuration
./engine_config --uci            # Export UCI options
./engine_config --reset          # Reset to defaults
```

#### Available Parameters
- **Hash**: 1-32768 MB (memory for transposition table)
- **Threads**: 1-128 (parallel search threads)
- **MultiPV**: 1-500 (number of best lines to show)
- **NUMA Offset**: 0-7 (memory allocation optimization)
- **Table Memory**: 1-8192 MB (tablebase cache)
- **Book File**: Path to opening book file
- **SyzygyPath**: Path to Syzygy tablebase files
- **Time settings**: Overhead, usage percentage, reporting

### Console Play

#### Starting the Game
1. Run the executable: `./chess_engine`
2. Choose your color (White or Black)
3. The game begins with the standard chess starting position

### Making Moves
- Use algebraic notation: `e2e4` (move piece from e2 to e4)
- Special moves:
  - **Castling**: Move king two squares (`e1g1` for kingside, `e1c1` for queenside)
  - **En passant**: Capture the pawn that just moved two squares
  - **Pawn promotion**: Add piece letter at end (`e7e8q` for queen promotion)
    - `q` = Queen, `r` = Rook, `b` = Bishop, `n` = Knight

### Commands
- `help` or `h` - Show help information
- `board` or `b` - Display current board
- `status` or `s` - Show game status
- `quit` or `q` - Exit the game

### Example Game Session
```
========================================
         WELCOME TO CHESS ENGINE
========================================

Do you want to play as White or Black? (w/b): w

Game setup complete!
You are playing as White
Enter moves in algebraic notation (e.g., 'e2e4')
Type 'help' for more commands.

  +---+---+---+---+---+---+---+---+
8 | r | n | b | q | k | b | n | r |
  +---+---+---+---+---+---+---+---+
7 | p | p | p | p | p | p | p | p |
  +---+---+---+---+---+---+---+---+
6 |   |   |   |   |   |   |   |   |
  +---+---+---+---+---+---+---+---+
5 |   |   |   |   |   |   |   |   |
  +---+---+---+---+---+---+---+---+
4 |   |   |   |   |   |   |   |   |
  +---+---+---+---+---+---+---+---+
3 |   |   |   |   |   |   |   |   |
  +---+---+---+---+---+---+---+---+
2 | P | P | P | P | P | P | P | P |
  +---+---+---+---+---+---+---+---+
1 | R | N | B | Q | K | B | N | R |
  +---+---+---+---+---+---+---+---+
    a   b   c   d   e   f   g   h

Current player: White
Game in progress

White to move: e2e4
Move e2e4 played successfully!

Computer is thinking...
Computer plays: e7e5
```

## Technical Details

### Architecture
The chess engine follows object-oriented design principles with clear separation of concerns:

- **ChessTypes.h**: Defines core types, enums, and utility functions
- **Piece hierarchy**: Abstract base class with specific implementations for each piece type
- **ChessBoard**: Manages the 8x8 board state and piece positions
- **Move**: Represents chess moves with support for special move types
- **ChessGame**: Handles game logic, turn management, and rule enforcement
- **ChessEngine**: AI implementation with minimax and position evaluation
- **ChessUI**: User interface and input/output handling

### AI Algorithm
The AI uses a minimax algorithm with alpha-beta pruning:
1. **Search depth**: Configurable depth (default 4 moves)
2. **Position evaluation**: Combines material value and positional factors
3. **Move ordering**: Prioritizes captures and promising moves
4. **Alpha-beta pruning**: Eliminates unnecessary search branches

### Performance
- **Search efficiency**: Alpha-beta pruning reduces search space significantly
- **Move generation**: Optimized move generation for each piece type
- **Memory management**: Smart pointers for automatic memory management
- **Compilation**: Optimized builds with -O2 flag for better performance

## Contributing

This is a complete, self-contained chess engine implementation. The code is well-documented and follows modern C++ best practices.

## License

This project is provided as-is for educational and personal use.

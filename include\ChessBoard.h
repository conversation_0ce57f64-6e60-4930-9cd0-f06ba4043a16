#pragma once

#include "ChessTypes.h"
#include "Piece.h"
#include <array>
#include <memory>
#include <string>

class ChessBoard {
private:
    std::array<std::array<std::unique_ptr<Piece>, 8>, 8> board_;
    Position enPassantTarget_;  // Position where en passant capture is possible
    bool whiteCanCastleKingside_;
    bool whiteCanCastleQueenside_;
    bool blackCanCastleKingside_;
    bool blackCanCastleQueenside_;

public:
    ChessBoard();
    ChessBoard(const ChessBoard& other);  // Copy constructor
    ChessBoard& operator=(const ChessBoard& other);  // Assignment operator
    ~ChessBoard() = default;

    // Board setup
    void setupInitialPosition();
    void clearBoard();
    
    // Piece access
    const Piece* getPiece(const Position& pos) const;
    Piece* getPiece(const Position& pos);
    void setPiece(const Position& pos, std::unique_ptr<Piece> piece);
    void removePiece(const Position& pos);
    
    // Move pieces
    bool movePiece(const Position& from, const Position& to);
    bool isValidPosition(const Position& pos) const;
    
    // Game state queries
    bool isSquareEmpty(const Position& pos) const;
    bool isSquareOccupiedBy(const Position& pos, Color color) const;
    bool isSquareAttackedBy(const Position& pos, Color attackingColor) const;
    Position findKing(Color color) const;
    bool isInCheck(Color color) const;
    bool isCheckmate(Color color) const;
    bool isStalemate(Color color) const;
    
    // Castling rights
    bool canCastleKingside(Color color) const;
    bool canCastleQueenside(Color color) const;
    void setCastlingRights(Color color, bool kingside, bool queenside);
    
    // En passant
    Position getEnPassantTarget() const { return enPassantTarget_; }
    void setEnPassantTarget(const Position& pos) { enPassantTarget_ = pos; }
    void clearEnPassantTarget() { enPassantTarget_ = Position(-1, -1); }
    
    // Utility functions
    std::vector<Position> getAllPiecesOfColor(Color color) const;
    std::vector<Position> getAllValidMoves(Color color) const;
    bool hasValidMoves(Color color) const;
    
    // Display
    void display() const;
    std::string toString() const;
    
    // Path checking for sliding pieces
    bool isPathClear(const Position& from, const Position& to) const;
    
private:
    void copyFrom(const ChessBoard& other);
};

#pragma once

#include "ChessTypes.h"
#include <chrono>
#include <vector>

// Time management class for chess engine
class TimeManager {
public:
    // Time allocation modes
    enum class Mode {
        FIXED_DEPTH,    // Fixed search depth
        FIXED_TIME,     // Fixed time per move
        TOURNAMENT,     // Tournament time control
        INFINITE        // Search until stopped
    };
    
    // Time control parameters
    struct TimeControl {
        int timeMs;         // Time left in milliseconds
        int incrementMs;    // Increment per move in milliseconds
        int movesToGo;      // Moves until next time control (0 = no limit)
        int depth;          // Maximum depth (0 = no limit)
        int nodes;          // Maximum nodes (0 = no limit)
        int moveTime;       // Fixed time per move (0 = use time control)
        
        TimeControl()
            : timeMs(0), incrementMs(0), movesToGo(0),
              depth(0), nodes(0), moveTime(0) {}
    };
    
private:
    // Current mode
    Mode mode_;
    
    // Time control for white and black
    TimeControl whiteTime_;
    TimeControl blackTime_;
    
    // Current side to move
    Color sideToMove_;
    
    // Search start time
    std::chrono::steady_clock::time_point startTime_;
    
    // Allocated time for current move
    int allocatedTimeMs_;
    int optimalTimeMs_;
    int maximumTimeMs_;
    
    // Move history for time allocation
    std::vector<int> moveTimesMs_;
    
    // Time usage factors
    double timeUsageFactor_;
    double panicFactor_;
    
    // Overhead time in milliseconds
    int overheadMs_;

    // Panic threshold in ms (when remaining time below this, play instantly)
    int panicThresholdMs_;
    
public:
    TimeManager();
    
    // Set time control
    void setTimeControl(Color side, const TimeControl& tc);
    
    // Set current side to move
    void setSideToMove(Color side) { sideToMove_ = side; }
    
    // Set search mode
    void setMode(Mode mode) { mode_ = mode; }
    
    // Set time usage factor (0.0 - 1.0)
    void setTimeUsageFactor(double factor) { timeUsageFactor_ = factor; }
    
    // Set panic factor (1.0 - 5.0)
    void setPanicFactor(double factor) { panicFactor_ = factor; }
    
    // Set overhead time in milliseconds
    void setOverheadMs(int ms) { overheadMs_ = ms; }
    
    // Set panic threshold (ms)
    void setPanicThresholdMs(int ms) { panicThresholdMs_ = ms; }

    // Start the clock for a new search
    void startClock();
    
    // Calculate time allocation for current move
    void calculateTimeForMove();
    
    // Check if time is up
    bool isTimeUp() const;
    
    // Check if we should stop search (soft limit)
    bool shouldStopSearch() const;

    // Get remaining time for side to move
    int getRemainingTimeMs() const;
    
    // Get elapsed time in milliseconds
    int getElapsedTimeMs() const;
    
    // Get allocated time for current move
    int getAllocatedTimeMs() const { return allocatedTimeMs_; }
    
    // Get optimal time for current move
    int getOptimalTimeMs() const { return optimalTimeMs_; }
    
    // Get maximum time for current move
    int getMaximumTimeMs() const { return maximumTimeMs_; }
    
    // Record time used for a move
    void recordMoveTime(int timeMs);
    
    // Get current mode
    Mode getMode() const { return mode_; }
    
    // Get time control for a side
    const TimeControl& getTimeControl(Color side) const {
        return (side == Color::WHITE) ? whiteTime_ : blackTime_;
    }
    
private:
    // Calculate time for tournament mode
    void calculateTournamentTime();
    
    // Calculate average move time
    int calculateAverageMoveTime() const;
    
    // Estimate remaining moves in the game
    int estimateRemainingMoves() const;
    
    // Adjust time based on game phase
    int adjustTimeForGamePhase(int baseTime) const;
};
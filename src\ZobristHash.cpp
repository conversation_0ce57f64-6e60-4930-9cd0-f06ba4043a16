#include "ZobristHash.h"
#include <random>

// Static member definitions
std::array<std::array<std::array<uint64_t, 64>, 6>, 2> ZobristHash::pieceKeys;
std::array<uint64_t, 16> ZobristHash::castlingKeys;
std::array<uint64_t, 8> ZobristHash::enPassantKeys;
uint64_t ZobristHash::sideToMoveKey;
bool ZobristHash::initialized = false;

void ZobristHash::initialize() {
    if (initialized) return;
    
    std::mt19937_64 rng(0x12345678ULL); // Fixed seed for reproducibility
    
    // Initialize piece keys [color][piece_type][square]
    for (int color = 0; color < 2; ++color) {
        for (int piece = 0; piece < 6; ++piece) {
            for (int square = 0; square < 64; ++square) {
                pieceKeys[color][piece][square] = rng();
            }
        }
    }
    
    // Initialize castling keys
    for (int i = 0; i < 16; ++i) {
        castlingKeys[i] = rng();
    }
    
    // Initialize en passant keys
    for (int i = 0; i < 8; ++i) {
        enPassantKeys[i] = rng();
    }
    
    sideToMoveKey = rng();
    initialized = true;
}

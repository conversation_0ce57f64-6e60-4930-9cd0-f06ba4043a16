#pragma once

#include "ChessTypes.h"
#include "ChessBoard.h"
#include "Piece.h"
#include <random>
#include <array>

class ZobristHash
{
private:
    // Zobrist hash tables
    static std::array<std::array<std::array<uint64_t, 64>, 6>, 2> pieceKeys;
    static std::array<uint64_t, 16> castlingKeys;
    static std::array<uint64_t, 8> enPassantKeys;
    static uint64_t sideToMoveKey;
    static bool initialized;

    static void initialize();

public:
    static uint64_t getPieceKey(Color color, PieceType piece, int square)
    {
        if (!initialized)
            initialize();
        return pieceKeys[static_cast<int>(color)][static_cast<int>(piece) - 1][square];
    }

    static uint64_t getCastlingKey(int castlingRights)
    {
        if (!initialized)
            initialize();
        return castlingKeys[castlingRights & 15];
    }

    static uint64_t getEnPassantKey(int file)
    {
        if (!initialized)
            initialize();
        return (file >= 0 && file < 8) ? enPassantKeys[file] : 0ULL;
    }

    static uint64_t getSideToMoveKey()
    {
        if (!initialized)
            initialize();
        return sideToMoveKey;
    }

    // Generate hash for a position
    static uint64_t generateHash(const ChessBoard &board, Color sideToMove,
                                 int castlingRights, int enPassantFile)
    {
        if (!initialized)
            initialize();

        uint64_t hash = 0ULL;

        // Hash pieces
        for (int rank = 0; rank < 8; ++rank)
        {
            for (int file = 0; file < 8; ++file)
            {
                const Piece *piece = board.getPiece(Position(rank, file));
                if (piece)
                {
                    int square = rank * 8 + file;
                    hash ^= getPieceKey(piece->getColor(), piece->getType(), square);
                }
            }
        }

        // Hash side to move
        if (sideToMove == Color::BLACK)
        {
            hash ^= sideToMoveKey;
        }

        // Hash castling rights
        hash ^= getCastlingKey(castlingRights);

        // Hash en passant
        if (enPassantFile >= 0)
        {
            hash ^= getEnPassantKey(enPassantFile);
        }

        return hash;
    }

    // Incremental hash updates
    static uint64_t updateHashMovePiece(uint64_t hash, Color color, PieceType piece,
                                        int fromSquare, int toSquare)
    {
        hash ^= getPieceKey(color, piece, fromSquare);
        hash ^= getPieceKey(color, piece, toSquare);
        return hash;
    }

    static uint64_t updateHashAddPiece(uint64_t hash, Color color, PieceType piece, int square)
    {
        return hash ^ getPieceKey(color, piece, square);
    }

    static uint64_t updateHashRemovePiece(uint64_t hash, Color color, PieceType piece, int square)
    {
        return hash ^ getPieceKey(color, piece, square);
    }

    static uint64_t updateHashSideToMove(uint64_t hash)
    {
        return hash ^ sideToMoveKey;
    }

    static uint64_t updateHashCastling(uint64_t hash, int oldRights, int newRights)
    {
        hash ^= getCastlingKey(oldRights);
        hash ^= getCastlingKey(newRights);
        return hash;
    }

    static uint64_t updateHashEnPassant(uint64_t hash, int oldFile, int newFile)
    {
        if (oldFile >= 0)
            hash ^= getEnPassantKey(oldFile);
        if (newFile >= 0)
            hash ^= getEnPassantKey(newFile);
        return hash;
    }
};

# Chess Engine Makefile

# Compiler settings
CXX = g++
CXXFLAGS = -std=c++17 -Wall -Wextra -O2
INCLUDES = -Iinclude
TARGET = chess_engine

# Directories
SRCDIR = src
INCDIR = include
OBJDIR = obj
TESTDIR = tests

# Source files
SOURCES = $(wildcard $(SRCDIR)/*.cpp) main.cpp
OBJECTS = $(SOURCES:%.cpp=$(OBJDIR)/%.o)
ENGINE_OBJECTS = $(filter-out $(OBJDIR)/main.o, $(OBJECTS))

# Test files
TEST_TARGET = test_chess_engine

# UCI target
UCI_TARGET = chess_engine_uci

# Default target
all: $(TARGET)

# Create object directory
$(OBJDIR):
	mkdir -p $(OBJDIR)
	mkdir -p $(OBJDIR)/$(SRCDIR)

# Build target
$(TARGET): $(OBJDIR) $(OBJECTS)
	$(CXX) $(OBJECTS) -o $(TARGET)

# Compile source files
$(OBJDIR)/%.o: %.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# Clean build files
clean:
	rm -rf $(OBJDIR) $(TARGET) $(TEST_TARGET) $(UCI_TARGET) engine_config config_demo
	rm -f *.pgn *.ini

# Debug build
debug: CXXFLAGS += -g -DDEBUG
debug: $(TARGET)

# Install (copy to system path)
install: $(TARGET)
	cp $(TARGET) /usr/local/bin/

# Uninstall
uninstall:
	rm -f /usr/local/bin/$(TARGET)

# Test target
$(TEST_TARGET): $(OBJDIR) $(ENGINE_OBJECTS)
	$(CXX) $(CXXFLAGS) $(INCLUDES) $(TESTDIR)/test_chess_engine.cpp $(ENGINE_OBJECTS) -o $(TEST_TARGET)

# UCI target
$(UCI_TARGET): $(OBJDIR) $(ENGINE_OBJECTS)
	$(CXX) $(CXXFLAGS) $(INCLUDES) uci_main.cpp $(ENGINE_OBJECTS) -o $(UCI_TARGET)

# Configuration tool
engine_config: $(OBJDIR) $(ENGINE_OBJECTS)
	$(CXX) $(CXXFLAGS) $(INCLUDES) config_main.cpp $(ENGINE_OBJECTS) -o engine_config

# Configuration demo
config_demo: $(OBJDIR) $(ENGINE_OBJECTS)
	$(CXX) $(CXXFLAGS) $(INCLUDES) demo_main.cpp $(ENGINE_OBJECTS) -o config_demo

# Run tests
test: $(TEST_TARGET)
	./$(TEST_TARGET)

# Performance benchmark
benchmark: $(TARGET)
	@echo "=== PERFORMANCE BENCHMARK ==="
	@echo "Testing search performance..."
	@time echo "e2e4" | ./$(TARGET) w

# Quick build (single command)
quick:
	$(CXX) $(CXXFLAGS) $(INCLUDES) main.cpp $(SRCDIR)/*.cpp -o $(TARGET)

# Run the program
run: $(TARGET)
	./$(TARGET)

# Show help
help:
	@echo "Available targets:"
	@echo "  all       - Build the chess engine (default)"
	@echo "  debug     - Build with debug symbols"
	@echo "  test      - Build and run tests"
	@echo "  uci       - Build UCI version for GUI compatibility"
	@echo "  benchmark - Run performance benchmark"
	@echo "  quick     - Quick single-command build"
	@echo "  clean     - Remove build files"
	@echo "  run       - Build and run the program"
	@echo "  install   - Install to system path"
	@echo "  uninstall - Remove from system path"
	@echo "  help      - Show this help message"

# UCI build
uci: $(UCI_TARGET)

.PHONY: all clean debug install uninstall run help test benchmark quick uci

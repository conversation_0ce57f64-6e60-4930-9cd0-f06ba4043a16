# Setup VibeChess Engine dengan n Croissant GUI

Panduan lengkap untuk menggunakan VibeChess Engine dengan n Croissant chess GUI.

## Langkah 1: Build Engine

```bash
# Build UCI engine
g++ -std=c++17 -Iinclude -O2 uci_main.cpp src/*.cpp -o chess_engine_uci
```

## Langkah 2: Setup di n Croissant

1. **Buka n Croissant**
2. **Masuk ke Engine Settings**
   - Klik menu "Engine" atau "Settings"
   - Pilih "Manage Engines" atau "Add Engine"

3. **Tambah Engine Baru**
   - Klik "Add" atau "New Engine"
   - Browse ke lokasi file `chess_engine_uci.exe`
   - Nama engine: "VibeChess"
   - Protocol: UCI

4. **Konfigurasi Parameter**
   - Hash: 64-512 MB (sesuai RAM yang tersedia)
   - Threads: 1-4 (sesuai jumlah core CPU)
   - MultiPV: 1 untuk permainan, 2-5 untuk analisis

## Langkah 3: Konfigurasi Engine

### Menggunakan Configuration Tool

```bash
# Build configuration tool
g++ -std=c++17 -Iinclude -O2 config_main.cpp src/EngineConfig.cpp src/EngineConfigUI.cpp -o engine_config

# Jalankan konfigurasi interaktif
./engine_config
```

### Parameter Penting untuk n Croissant

1. **Hash Table Size**
   - Minimum: 64 MB
   - Recommended: 128-512 MB
   - Maximum: Sesuai RAM yang tersedia

2. **Threads**
   - Single core: 1 thread
   - Dual core: 1-2 threads
   - Quad core: 2-4 threads
   - 8+ cores: 4-8 threads

3. **MultiPV**
   - Permainan normal: 1
   - Analisis: 2-5
   - Analisis mendalam: 5-10

4. **Time Management**
   - Overhead ms: 50-100 (untuk koneksi yang stabil)
   - Time Usage: 80-95% (untuk manajemen waktu optimal)

## Langkah 4: Test Engine

1. **Test Koneksi**
   - Buka n Croissant
   - Pilih VibeChess sebagai engine
   - Pastikan engine merespons dengan "readyok"

2. **Test Permainan**
   - Mulai permainan baru
   - Set engine sebagai lawan
   - Lakukan beberapa gerakan untuk test

3. **Test Analisis**
   - Buka mode analisis
   - Pastikan engine menampilkan evaluasi dan variasi

## Parameter Konfigurasi Lengkap

### Memory Settings
- **Hash**: 32-32768 MB (ukuran hash table)
- **Table Memory**: 64-8192 MB (cache untuk tablebase)
- **NUMA Offset**: 0-7 (optimasi untuk sistem multi-processor)

### Search Settings
- **Threads**: 1-128 (jumlah thread pencarian)
- **MultiPV**: 1-500 (jumlah variasi yang ditampilkan)
- **Null Move**: true/false (null move pruning)

### Time Management
- **Overhead ms**: 0-5000 (overhead komunikasi)
- **Time Usage**: 0-100% (persentase waktu yang digunakan)
- **Minimal Reporting**: 0-10000 ms (interval laporan minimum)

### Files & Paths
- **Book File**: Path ke opening book (.bin, .ctg, .abk)
- **Log File**: Path untuk file log engine
- **Hash File Name**: Path untuk persistent hash storage
- **SyzygyPath**: Path ke Syzygy tablebase files

### Opening Book
- **OwnBook**: true/false (gunakan opening book internal)
- **Book Moves**: 0-10000 (maksimal gerakan dari book)

### Tablebase
- **SyzygyPath**: Path ke direktori Syzygy files
- **Syzygy Probe Depth**: 1-100 (kedalaman minimum probe)

## Tips Optimasi

### Untuk Permainan Cepat
```ini
Hash=64
Threads=1
MultiPV=1
Overhead ms=30
Time Usage=90
```

### Untuk Analisis Mendalam
```ini
Hash=256
Threads=4
MultiPV=5
Overhead ms=100
Time Usage=95
```

### Untuk Sistem Powerful
```ini
Hash=1024
Threads=8
MultiPV=3
Table Memory=256
SyzygyPath=C:\Tablebase\Syzygy
```

## Troubleshooting

### Engine Tidak Terdeteksi
- Pastikan file `chess_engine_uci.exe` dapat dijalankan
- Check path file sudah benar
- Pastikan tidak ada antivirus yang memblokir

### Performa Lambat
- Kurangi Hash size jika RAM terbatas
- Kurangi jumlah Threads jika CPU overload
- Increase Overhead ms jika koneksi tidak stabil

### Error saat Analisis
- Check MultiPV setting tidak terlalu tinggi
- Pastikan Hash size cukup besar
- Verify engine configuration valid

## Contoh Konfigurasi

### Gaming Setup (4GB RAM, 4 cores)
```bash
./engine_config --save gaming.ini
# Edit manual atau gunakan interface:
# Hash=128, Threads=2, MultiPV=1, Overhead ms=50
```

### Analysis Setup (8GB RAM, 8 cores)
```bash
./engine_config --save analysis.ini
# Hash=512, Threads=4, MultiPV=3, Overhead ms=100
```

### Tournament Setup
```bash
./engine_config --save tournament.ini
# Hash=256, Threads=1, MultiPV=1, Overhead ms=30, Time Usage=95
```

## Dukungan

Jika mengalami masalah:
1. Check file `ENGINE_CONFIG_README.md` untuk detail parameter
2. Test engine dengan command line terlebih dahulu
3. Verify UCI communication dengan: `echo "uci" | ./chess_engine_uci.exe`
4. Check log files jika ada error

Engine ini telah ditest dan kompatibel dengan n Croissant serta GUI UCI lainnya.

#include "ChessUI.h"
#include "UCIProtocol.h"
#include <iostream>
#include <exception>
#include <string>

int main(int argc, char *argv[])
{
    try
    {
        // Check for UCI mode
        bool uciMode = false;
        for (int i = 1; i < argc; ++i)
        {
            std::string arg = argv[i];
            if (arg == "--uci" || arg == "-u")
            {
                uciMode = true;
                break;
            }
        }

        if (uciMode)
        {
            // Run in UCI mode for GUI compatibility
            UCIProtocol uci;
            uci.run();
        }
        else
        {
            // Run in console mode
            std::cout << "Advanced Chess Engine\n";
            std::cout << "Usage: " << argv[0] << " [--uci|-u] for UCI mode\n";
            std::cout << "Starting console mode...\n\n";

            ChessUI ui;
            ui.run();
        }
    }
    catch (const std::exception &e)
    {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    catch (...)
    {
        std::cerr << "Unknown error occurred!" << std::endl;
        return 1;
    }

    return 0;
}
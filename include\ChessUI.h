#pragma once

#include "ChessGame.h"
#include "ChessEngine.h"
#include "PGNManager.h"
#include <string>

class ChessUI
{
private:
    ChessGame game_;
    ChessEngine engine_;
    PGNManager pgnManager_;
    bool humanIsWhite_;
    std::string currentGameFile_;

public:
    ChessUI();

    // Main game loop
    void run();

    // Game setup
    void setupGame();
    bool askHumanColor();

    // Input/Output
    std::string getPlayerInput();
    Move parseMove(const std::string &input);
    void displayWelcome();
    void displayHelp();
    void displayBoard();
    void displayGameStatus();
    void displayMoveResult(bool success, const std::string &move);
    void displayGameOver();

    // Game flow
    void playHumanTurn();
    void playComputerTurn();
    bool processCommand(const std::string &input);

    // Analysis features
    void analyzePosition();
    void showHint();

    // Game management
    void saveGame();
    void loadGame();
    void newGame();
    void showGameInfo();

    // Utility
    void clearScreen();
    void waitForEnter();
    std::string toLowerCase(const std::string &str);
};

#include "PGNManager.h"
#include <fstream>
#include <sstream>
#include <iomanip>
#include <chrono>
#include <ctime>

PGNManager::PGNManager() {
    clear();
}

void PGNManager::setHeader(const std::string& key, const std::string& value) {
    headers_[key] = value;
}

std::string PGNManager::getHeader(const std::string& key) const {
    auto it = headers_.find(key);
    return (it != headers_.end()) ? it->second : "";
}

void PGNManager::setStandardHeaders(const std::string& white, const std::string& black, 
                                   const std::string& event, const std::string& site) {
    headers_["Event"] = event;
    headers_["Site"] = site;
    headers_["Date"] = getCurrentDate();
    headers_["Round"] = "1";
    headers_["White"] = white;
    headers_["Black"] = black;
    headers_["Result"] = "*"; // Game in progress
}

void PGNManager::addMove(const Move& move, const ChessGame& game) {
    std::string algebraic = moveToAlgebraic(move, game);
    addMove(algebraic);
}

void PGNManager::addMove(const std::string& algebraicMove) {
    moveList_.push_back(algebraicMove);
}

void PGNManager::setResult(const std::string& result) {
    result_ = result;
    headers_["Result"] = result;
}

std::string PGNManager::generatePGN() const {
    std::stringstream pgn;
    
    // Headers
    for (const auto& header : headers_) {
        pgn << "[" << header.first << " \"" << header.second << "\"]\n";
    }
    pgn << "\n";
    
    // Moves
    pgn << getMovesString();
    
    // Result
    if (!result_.empty()) {
        pgn << " " << result_;
    }
    
    pgn << "\n";
    return pgn.str();
}

std::string PGNManager::getMovesString() const {
    std::stringstream moves;
    
    for (size_t i = 0; i < moveList_.size(); ++i) {
        if (i % 2 == 0) {
            // White move
            moves << formatMoveNumber((i / 2) + 1, true) << moveList_[i];
        } else {
            // Black move
            moves << " " << moveList_[i];
            if (i < moveList_.size() - 1) {
                moves << " ";
            }
        }
    }
    
    return moves.str();
}

bool PGNManager::saveToFile(const std::string& filename) const {
    std::ofstream file(filename);
    if (!file.is_open()) {
        return false;
    }
    
    file << generatePGN();
    file.close();
    return true;
}

bool PGNManager::loadFromFile(const std::string& filename) {
    std::ifstream file(filename);
    if (!file.is_open()) {
        return false;
    }
    
    clear();
    
    std::string line;
    bool inHeaders = true;
    std::stringstream moveText;
    
    while (std::getline(file, line)) {
        if (line.empty()) {
            inHeaders = false;
            continue;
        }
        
        if (inHeaders && line[0] == '[') {
            // Parse header
            size_t keyStart = 1;
            size_t keyEnd = line.find(' ', keyStart);
            size_t valueStart = line.find('"', keyEnd) + 1;
            size_t valueEnd = line.rfind('"');
            
            if (keyEnd != std::string::npos && valueStart != std::string::npos && valueEnd != std::string::npos) {
                std::string key = line.substr(keyStart, keyEnd - keyStart);
                std::string value = line.substr(valueStart, valueEnd - valueStart);
                headers_[key] = value;
            }
        } else if (!inHeaders) {
            moveText << line << " ";
        }
    }
    
    // Parse moves (simplified)
    std::string moves = moveText.str();
    std::istringstream iss(moves);
    std::string token;
    
    while (iss >> token) {
        // Skip move numbers and result
        if (token.find('.') == std::string::npos && 
            token != "1-0" && token != "0-1" && token != "1/2-1/2" && token != "*") {
            moveList_.push_back(token);
        } else if (token == "1-0" || token == "0-1" || token == "1/2-1/2") {
            result_ = token;
        }
    }
    
    file.close();
    return true;
}

void PGNManager::clear() {
    headers_.clear();
    moveList_.clear();
    result_ = "*";
}

std::string PGNManager::getCurrentDate() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto tm = *std::localtime(&time_t);
    
    std::stringstream date;
    date << std::put_time(&tm, "%Y.%m.%d");
    return date.str();
}

std::string PGNManager::moveToAlgebraic(const Move& move, const ChessGame& game) const {
    // Simplified algebraic notation
    // In a full implementation, this would handle disambiguation, check/checkmate notation, etc.
    return move.toAlgebraic();
}

std::string PGNManager::formatMoveNumber(int moveNumber, bool isWhite) const {
    std::stringstream formatted;
    formatted << moveNumber << ". ";
    return formatted.str();
}

#pragma once

#include "Piece.h"
#include "ChessBoard.h"

// Pawn piece implementation
class Pawn : public Piece {
public:
    Pawn(Color color) : Piece(color, PieceType::PAWN) {}
    
    std::vector<Position> getPossibleMoves(const Position& from, const ChessBoard& board) const override;
    bool canMoveTo(const Position& from, const Position& to, const ChessBoard& board) const override;
    std::unique_ptr<Piece> clone() const override {
        auto pawn = std::make_unique<Pawn>(color_);
        pawn->setMoved(hasMoved_);
        return pawn;
    }
};

// Rook piece implementation
class Rook : public Piece {
public:
    Rook(Color color) : Piece(color, PieceType::ROOK) {}
    
    std::vector<Position> getPossibleMoves(const Position& from, const ChessBoard& board) const override;
    bool canMoveTo(const Position& from, const Position& to, const ChessBoard& board) const override;
    std::unique_ptr<Piece> clone() const override {
        auto rook = std::make_unique<Rook>(color_);
        rook->setMoved(hasMoved_);
        return rook;
    }
};

// Knight piece implementation
class Knight : public Piece {
public:
    Knight(Color color) : Piece(color, PieceType::KNIGHT) {}
    
    std::vector<Position> getPossibleMoves(const Position& from, const ChessBoard& board) const override;
    bool canMoveTo(const Position& from, const Position& to, const ChessBoard& board) const override;
    std::unique_ptr<Piece> clone() const override {
        auto knight = std::make_unique<Knight>(color_);
        knight->setMoved(hasMoved_);
        return knight;
    }
};

// Bishop piece implementation
class Bishop : public Piece {
public:
    Bishop(Color color) : Piece(color, PieceType::BISHOP) {}
    
    std::vector<Position> getPossibleMoves(const Position& from, const ChessBoard& board) const override;
    bool canMoveTo(const Position& from, const Position& to, const ChessBoard& board) const override;
    std::unique_ptr<Piece> clone() const override {
        auto bishop = std::make_unique<Bishop>(color_);
        bishop->setMoved(hasMoved_);
        return bishop;
    }
};

// Queen piece implementation
class Queen : public Piece {
public:
    Queen(Color color) : Piece(color, PieceType::QUEEN) {}
    
    std::vector<Position> getPossibleMoves(const Position& from, const ChessBoard& board) const override;
    bool canMoveTo(const Position& from, const Position& to, const ChessBoard& board) const override;
    std::unique_ptr<Piece> clone() const override {
        auto queen = std::make_unique<Queen>(color_);
        queen->setMoved(hasMoved_);
        return queen;
    }
};

// King piece implementation
class King : public Piece {
public:
    King(Color color) : Piece(color, PieceType::KING) {}
    
    std::vector<Position> getPossibleMoves(const Position& from, const ChessBoard& board) const override;
    bool canMoveTo(const Position& from, const Position& to, const ChessBoard& board) const override;
    std::unique_ptr<Piece> clone() const override {
        auto king = std::make_unique<King>(color_);
        king->setMoved(hasMoved_);
        return king;
    }
};

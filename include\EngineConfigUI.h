#pragma once

#include "EngineConfig.h"
#include <string>
#include <vector>
#include <functional>

// Interface untuk mengelola konfigurasi engine dengan tampilan yang user-friendly
class EngineConfigUI {
private:
    EngineConfig& config_;
    bool isRunning_;
    
    // Display helpers
    void clearScreen() const;
    void displayHeader() const;
    void displayParameterList() const;
    void displayParameterDetails(const std::string& paramName) const;
    void displayHelp() const;
    
    // Input helpers
    std::string getInput(const std::string& prompt) const;
    int getIntInput(const std::string& prompt, int min, int max) const;
    bool getBoolInput(const std::string& prompt) const;
    std::string getStringInput(const std::string& prompt) const;
    std::string getFileInput(const std::string& prompt, bool mustExist = false) const;
    
    // Menu handlers
    void handleViewParameters();
    void handleEditParameter();
    void handleResetParameter();
    void handleResetAll();
    void handleLoadConfig();
    void handleSaveConfig();
    void handleExportUCI();
    void handleImportFromUCI();
    
    // Parameter editing
    bool editParameter(const std::string& paramName);
    bool editSpinParameter(const ConfigParameter* param);
    bool editCheckParameter(const ConfigParameter* param);
    bool editStringParameter(const ConfigParameter* param);
    bool editComboParameter(const ConfigParameter* param);
    
    // Validation helpers
    bool validateFilePath(const std::string& path, bool mustExist = false) const;
    std::string formatParameterValue(const ConfigParameter* param) const;
    
    // Display formatting
    std::string centerText(const std::string& text, int width) const;
    std::string padRight(const std::string& text, int width) const;
    void printSeparator(char ch = '=', int width = 80) const;
    
public:
    explicit EngineConfigUI(EngineConfig& config);
    ~EngineConfigUI() = default;
    
    // Main interface
    void run();
    void displayMainMenu();
    bool processMenuChoice(const std::string& choice);
    
    // Quick access functions
    void quickSetup();
    void showCurrentConfig();
    void showParameterHelp(const std::string& paramName);
    
    // Utility functions
    static void waitForEnter();
    static std::string toLowerCase(const std::string& str);
    static std::string trim(const std::string& str);
};

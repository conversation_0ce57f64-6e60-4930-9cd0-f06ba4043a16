# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 4.0

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: ChessEngine
# Configurations: 
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:/Users/<USER>/Documents/Django/Chess-Engine/build/
# =============================================================================
# Object build statements for EXECUTABLE target chess_engine


#############################################
# Order-only phony target for chess_engine

build cmake_object_order_depends_target_chess_engine: phony || .

build CMakeFiles/chess_engine.dir/main.cpp.obj: CXX_COMPILER__chess_engine_unscanned_ C$:/Users/<USER>/Documents/Django/Chess-Engine/main.cpp || cmake_object_order_depends_target_chess_engine
  DEP_FILE = CMakeFiles\chess_engine.dir\main.cpp.obj.d
  FLAGS = -std=gnu++17 -Wall -Wextra -O2
  INCLUDES = -IC:/Users/<USER>/Documents/Django/Chess-Engine/include
  OBJECT_DIR = CMakeFiles\chess_engine.dir
  OBJECT_FILE_DIR = CMakeFiles\chess_engine.dir
  TARGET_COMPILE_PDB = CMakeFiles\chess_engine.dir\
  TARGET_PDB = chess_engine.pdb

build CMakeFiles/chess_engine.dir/src/ChessBoard.cpp.obj: CXX_COMPILER__chess_engine_unscanned_ C$:/Users/<USER>/Documents/Django/Chess-Engine/src/ChessBoard.cpp || cmake_object_order_depends_target_chess_engine
  DEP_FILE = CMakeFiles\chess_engine.dir\src\ChessBoard.cpp.obj.d
  FLAGS = -std=gnu++17 -Wall -Wextra -O2
  INCLUDES = -IC:/Users/<USER>/Documents/Django/Chess-Engine/include
  OBJECT_DIR = CMakeFiles\chess_engine.dir
  OBJECT_FILE_DIR = CMakeFiles\chess_engine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\chess_engine.dir\
  TARGET_PDB = chess_engine.pdb

build CMakeFiles/chess_engine.dir/src/ChessEngine.cpp.obj: CXX_COMPILER__chess_engine_unscanned_ C$:/Users/<USER>/Documents/Django/Chess-Engine/src/ChessEngine.cpp || cmake_object_order_depends_target_chess_engine
  DEP_FILE = CMakeFiles\chess_engine.dir\src\ChessEngine.cpp.obj.d
  FLAGS = -std=gnu++17 -Wall -Wextra -O2
  INCLUDES = -IC:/Users/<USER>/Documents/Django/Chess-Engine/include
  OBJECT_DIR = CMakeFiles\chess_engine.dir
  OBJECT_FILE_DIR = CMakeFiles\chess_engine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\chess_engine.dir\
  TARGET_PDB = chess_engine.pdb

build CMakeFiles/chess_engine.dir/src/ChessGame.cpp.obj: CXX_COMPILER__chess_engine_unscanned_ C$:/Users/<USER>/Documents/Django/Chess-Engine/src/ChessGame.cpp || cmake_object_order_depends_target_chess_engine
  DEP_FILE = CMakeFiles\chess_engine.dir\src\ChessGame.cpp.obj.d
  FLAGS = -std=gnu++17 -Wall -Wextra -O2
  INCLUDES = -IC:/Users/<USER>/Documents/Django/Chess-Engine/include
  OBJECT_DIR = CMakeFiles\chess_engine.dir
  OBJECT_FILE_DIR = CMakeFiles\chess_engine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\chess_engine.dir\
  TARGET_PDB = chess_engine.pdb

build CMakeFiles/chess_engine.dir/src/ChessUI.cpp.obj: CXX_COMPILER__chess_engine_unscanned_ C$:/Users/<USER>/Documents/Django/Chess-Engine/src/ChessUI.cpp || cmake_object_order_depends_target_chess_engine
  DEP_FILE = CMakeFiles\chess_engine.dir\src\ChessUI.cpp.obj.d
  FLAGS = -std=gnu++17 -Wall -Wextra -O2
  INCLUDES = -IC:/Users/<USER>/Documents/Django/Chess-Engine/include
  OBJECT_DIR = CMakeFiles\chess_engine.dir
  OBJECT_FILE_DIR = CMakeFiles\chess_engine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\chess_engine.dir\
  TARGET_PDB = chess_engine.pdb

build CMakeFiles/chess_engine.dir/src/OpeningBook.cpp.obj: CXX_COMPILER__chess_engine_unscanned_ C$:/Users/<USER>/Documents/Django/Chess-Engine/src/OpeningBook.cpp || cmake_object_order_depends_target_chess_engine
  DEP_FILE = CMakeFiles\chess_engine.dir\src\OpeningBook.cpp.obj.d
  FLAGS = -std=gnu++17 -Wall -Wextra -O2
  INCLUDES = -IC:/Users/<USER>/Documents/Django/Chess-Engine/include
  OBJECT_DIR = CMakeFiles\chess_engine.dir
  OBJECT_FILE_DIR = CMakeFiles\chess_engine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\chess_engine.dir\
  TARGET_PDB = chess_engine.pdb

build CMakeFiles/chess_engine.dir/src/Piece.cpp.obj: CXX_COMPILER__chess_engine_unscanned_ C$:/Users/<USER>/Documents/Django/Chess-Engine/src/Piece.cpp || cmake_object_order_depends_target_chess_engine
  DEP_FILE = CMakeFiles\chess_engine.dir\src\Piece.cpp.obj.d
  FLAGS = -std=gnu++17 -Wall -Wextra -O2
  INCLUDES = -IC:/Users/<USER>/Documents/Django/Chess-Engine/include
  OBJECT_DIR = CMakeFiles\chess_engine.dir
  OBJECT_FILE_DIR = CMakeFiles\chess_engine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\chess_engine.dir\
  TARGET_PDB = chess_engine.pdb

build CMakeFiles/chess_engine.dir/src/Pieces.cpp.obj: CXX_COMPILER__chess_engine_unscanned_ C$:/Users/<USER>/Documents/Django/Chess-Engine/src/Pieces.cpp || cmake_object_order_depends_target_chess_engine
  DEP_FILE = CMakeFiles\chess_engine.dir\src\Pieces.cpp.obj.d
  FLAGS = -std=gnu++17 -Wall -Wextra -O2
  INCLUDES = -IC:/Users/<USER>/Documents/Django/Chess-Engine/include
  OBJECT_DIR = CMakeFiles\chess_engine.dir
  OBJECT_FILE_DIR = CMakeFiles\chess_engine.dir\src
  TARGET_COMPILE_PDB = CMakeFiles\chess_engine.dir\
  TARGET_PDB = chess_engine.pdb


# =============================================================================
# Link build statements for EXECUTABLE target chess_engine


#############################################
# Link the executable chess_engine.exe

build chess_engine.exe: CXX_EXECUTABLE_LINKER__chess_engine_ CMakeFiles/chess_engine.dir/main.cpp.obj CMakeFiles/chess_engine.dir/src/ChessBoard.cpp.obj CMakeFiles/chess_engine.dir/src/ChessEngine.cpp.obj CMakeFiles/chess_engine.dir/src/ChessGame.cpp.obj CMakeFiles/chess_engine.dir/src/ChessUI.cpp.obj CMakeFiles/chess_engine.dir/src/OpeningBook.cpp.obj CMakeFiles/chess_engine.dir/src/Piece.cpp.obj CMakeFiles/chess_engine.dir/src/Pieces.cpp.obj
  LINK_LIBRARIES = -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32
  OBJECT_DIR = CMakeFiles\chess_engine.dir
  POST_BUILD = cd .
  PRE_LINK = cd .
  TARGET_COMPILE_PDB = CMakeFiles\chess_engine.dir\
  TARGET_FILE = chess_engine.exe
  TARGET_IMPLIB = libchess_engine.dll.a
  TARGET_PDB = chess_engine.pdb


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\Documents\Django\Chess-Engine\build && C:\mingw32\bin\cmake.exe -E echo "No interactive CMake dialog available.""
  DESC = No interactive CMake dialog available...
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = C:\WINDOWS\system32\cmd.exe /C "cd /D C:\Users\<USER>\Documents\Django\Chess-Engine\build && C:\mingw32\bin\cmake.exe --regenerate-during-build -SC:\Users\<USER>\Documents\Django\Chess-Engine -BC:\Users\<USER>\Documents\Django\Chess-Engine\build"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util

# =============================================================================
# Target aliases.

build chess_engine: phony chess_engine.exe

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/Documents/Django/Chess-Engine/build

build all: phony chess_engine.exe

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja C$:/Users/<USER>/Documents/Django/Chess-Engine/build/cmake_install.cmake: RERUN_CMAKE | C$:/Users/<USER>/Documents/Django/Chess-Engine/CMakeLists.txt C$:/mingw32/share/cmake-4.0/Modules/CMakeCCompiler.cmake.in C$:/mingw32/share/cmake-4.0/Modules/CMakeCCompilerABI.c C$:/mingw32/share/cmake-4.0/Modules/CMakeCInformation.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeCXXCompiler.cmake.in C$:/mingw32/share/cmake-4.0/Modules/CMakeCXXCompilerABI.cpp C$:/mingw32/share/cmake-4.0/Modules/CMakeCXXInformation.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeCommonLanguageInclude.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeCompilerIdDetection.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeDetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeDetermineCompilerSupport.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeDetermineRCCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeFindBinUtils.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeGenericSystem.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeInitializeConfigs.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeLanguageInformation.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeNinjaFindMake.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeParseImplicitLinkInfo.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeParseLibraryArchitecture.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeRCCompiler.cmake.in C$:/mingw32/share/cmake-4.0/Modules/CMakeRCInformation.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeSystem.cmake.in C$:/mingw32/share/cmake-4.0/Modules/CMakeSystemSpecificInformation.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeSystemSpecificInitialize.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeTestCompilerCommon.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeTestRCCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/CrayClang-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/GNU-C.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/GNU-CXX-CXXImportStd.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/GNU-CXX.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/GNU-FindBinUtils.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/GNU.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/LCC-C-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/OrangeC-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/TI-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/TIClang-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/Tasking-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Internal/CMakeCLinkerInformation.cmake C$:/mingw32/share/cmake-4.0/Modules/Internal/CMakeCXXLinkerInformation.cmake C$:/mingw32/share/cmake-4.0/Modules/Internal/CMakeCommonLinkerInformation.cmake C$:/mingw32/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake C$:/mingw32/share/cmake-4.0/Modules/Internal/CMakeInspectCLinker.cmake C$:/mingw32/share/cmake-4.0/Modules/Internal/CMakeInspectCXXLinker.cmake C$:/mingw32/share/cmake-4.0/Modules/Internal/FeatureTesting.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Linker/GNU.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Linker/Windows-C.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Linker/Windows-CXX.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Linker/Windows-GNU-C.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Linker/Windows-GNU-CXX.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Linker/Windows-GNU.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Windows-Determine-CXX.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Windows-GNU-C-ABI.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Windows-GNU-C.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Windows-GNU-CXX-ABI.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Windows-GNU-CXX.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Windows-GNU.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Windows-Initialize.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Windows-windres.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Windows.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/WindowsPaths.cmake CMakeCache.txt CMakeFiles/4.0.2/CMakeCCompiler.cmake CMakeFiles/4.0.2/CMakeCXXCompiler.cmake CMakeFiles/4.0.2/CMakeRCCompiler.cmake CMakeFiles/4.0.2/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:/Users/<USER>/Documents/Django/Chess-Engine/CMakeLists.txt C$:/mingw32/share/cmake-4.0/Modules/CMakeCCompiler.cmake.in C$:/mingw32/share/cmake-4.0/Modules/CMakeCCompilerABI.c C$:/mingw32/share/cmake-4.0/Modules/CMakeCInformation.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeCXXCompiler.cmake.in C$:/mingw32/share/cmake-4.0/Modules/CMakeCXXCompilerABI.cpp C$:/mingw32/share/cmake-4.0/Modules/CMakeCXXInformation.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeCommonLanguageInclude.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeCompilerIdDetection.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeDetermineCCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeDetermineCXXCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeDetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeDetermineCompilerABI.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeDetermineCompilerId.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeDetermineCompilerSupport.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeDetermineRCCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeDetermineSystem.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeFindBinUtils.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeGenericSystem.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeInitializeConfigs.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeLanguageInformation.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeNinjaFindMake.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeParseImplicitIncludeInfo.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeParseImplicitLinkInfo.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeParseLibraryArchitecture.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeRCCompiler.cmake.in C$:/mingw32/share/cmake-4.0/Modules/CMakeRCInformation.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeSystem.cmake.in C$:/mingw32/share/cmake-4.0/Modules/CMakeSystemSpecificInformation.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeSystemSpecificInitialize.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeTestCCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeTestCXXCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeTestCompilerCommon.cmake C$:/mingw32/share/cmake-4.0/Modules/CMakeTestRCCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/ADSP-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/ARMCC-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/ARMClang-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/AppleClang-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/Borland-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/Bruce-C-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/CMakeCommonCompilerMacros.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/Clang-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/Clang-DetermineCompilerInternal.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/Compaq-C-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/Compaq-CXX-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/Cray-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/CrayClang-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/Embarcadero-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/Fujitsu-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/FujitsuClang-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/GHS-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/GNU-C-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/GNU-C.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/GNU-CXX-CXXImportStd.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/GNU-CXX-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/GNU-CXX.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/GNU-FindBinUtils.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/GNU.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/HP-C-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/HP-CXX-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/IAR-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/IBMCPP-C-DetermineVersionInternal.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/IBMCPP-CXX-DetermineVersionInternal.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/IBMClang-C-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/IBMClang-CXX-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/Intel-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/IntelLLVM-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/LCC-C-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/LCC-CXX-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/MSVC-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/NVHPC-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/NVIDIA-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/OpenWatcom-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/OrangeC-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/PGI-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/PathScale-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/SCO-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/SDCC-C-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/SunPro-C-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/SunPro-CXX-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/TI-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/TIClang-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/Tasking-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/TinyCC-C-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/VisualAge-C-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/VisualAge-CXX-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/Watcom-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/XL-C-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/XL-CXX-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/XLClang-C-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/XLClang-CXX-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/zOS-C-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Compiler/zOS-CXX-DetermineCompiler.cmake C$:/mingw32/share/cmake-4.0/Modules/Internal/CMakeCLinkerInformation.cmake C$:/mingw32/share/cmake-4.0/Modules/Internal/CMakeCXXLinkerInformation.cmake C$:/mingw32/share/cmake-4.0/Modules/Internal/CMakeCommonLinkerInformation.cmake C$:/mingw32/share/cmake-4.0/Modules/Internal/CMakeDetermineLinkerId.cmake C$:/mingw32/share/cmake-4.0/Modules/Internal/CMakeInspectCLinker.cmake C$:/mingw32/share/cmake-4.0/Modules/Internal/CMakeInspectCXXLinker.cmake C$:/mingw32/share/cmake-4.0/Modules/Internal/FeatureTesting.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Linker/GNU.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Linker/Windows-C.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Linker/Windows-CXX.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Linker/Windows-GNU-C.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Linker/Windows-GNU-CXX.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Linker/Windows-GNU.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Windows-Determine-CXX.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Windows-GNU-C-ABI.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Windows-GNU-C.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Windows-GNU-CXX-ABI.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Windows-GNU-CXX.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Windows-GNU.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Windows-Initialize.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Windows-windres.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/Windows.cmake C$:/mingw32/share/cmake-4.0/Modules/Platform/WindowsPaths.cmake CMakeCache.txt CMakeFiles/4.0.2/CMakeCCompiler.cmake CMakeFiles/4.0.2/CMakeCXXCompiler.cmake CMakeFiles/4.0.2/CMakeRCCompiler.cmake CMakeFiles/4.0.2/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all

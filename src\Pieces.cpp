#include "Pieces.h"
#include "ChessBoard.h"
#include <cmath>

// Pawn implementation
std::vector<Position> Pawn::getPossibleMoves(const Position &from, const ChessBoard &board) const
{
    std::vector<Position> moves;

    int direction = (color_ == Color::WHITE) ? 1 : -1;
    int startRank = (color_ == Color::WHITE) ? 1 : 6;

    // Forward move
    Position oneForward(from.rank + direction, from.file);
    if (oneForward.isValid() && board.isSquareEmpty(oneForward))
    {
        moves.push_back(oneForward);

        // Two squares forward from starting position
        if (from.rank == startRank)
        {
            Position twoForward(from.rank + 2 * direction, from.file);
            if (twoForward.isValid() && board.isSquareEmpty(twoForward))
            {
                moves.push_back(twoForward);
            }
        }
    }

    // Diagonal captures
    Position leftCapture(from.rank + direction, from.file - 1);
    if (leftCapture.isValid() &&
        (board.isSquareOccupiedBy(leftCapture, oppositeColor(color_)) ||
         leftCapture == board.getEnPassantTarget()))
    {
        moves.push_back(leftCapture);
    }

    Position rightCapture(from.rank + direction, from.file + 1);
    if (rightCapture.isValid() &&
        (board.isSquareOccupiedBy(rightCapture, oppositeColor(color_)) ||
         rightCapture == board.getEnPassantTarget()))
    {
        moves.push_back(rightCapture);
    }

    return moves;
}

bool Pawn::canMoveTo(const Position &from, const Position &to, const ChessBoard &board) const
{
    int direction = (color_ == Color::WHITE) ? 1 : -1;
    int rankDiff = to.rank - from.rank;
    int fileDiff = std::abs(to.file - from.file);

    // Forward moves
    if (fileDiff == 0)
    {
        if (rankDiff == direction && board.isSquareEmpty(to))
        {
            return true;
        }
        // Two squares from starting position
        if (rankDiff == 2 * direction && !hasMoved_ &&
            board.isSquareEmpty(to) && board.isSquareEmpty(Position(from.rank + direction, from.file)))
        {
            return true;
        }
    }
    // Diagonal captures
    else if (fileDiff == 1 && rankDiff == direction)
    {
        return board.isSquareOccupiedBy(to, oppositeColor(color_)) ||
               to == board.getEnPassantTarget();
    }

    return false;
}

// Rook implementation
std::vector<Position> Rook::getPossibleMoves(const Position &from, const ChessBoard &board) const
{
    std::vector<Position> moves;

    // Horizontal and vertical directions
    int directions[4][2] = {{0, 1}, {0, -1}, {1, 0}, {-1, 0}};

    for (int i = 0; i < 4; ++i)
    {
        int rankStep = directions[i][0];
        int fileStep = directions[i][1];

        Position current(from.rank + rankStep, from.file + fileStep);
        while (current.isValid())
        {
            if (board.isSquareEmpty(current))
            {
                moves.push_back(current);
            }
            else
            {
                if (board.isSquareOccupiedBy(current, oppositeColor(color_)))
                {
                    moves.push_back(current);
                }
                break; // Stop at any piece
            }
            current.rank += rankStep;
            current.file += fileStep;
        }
    }

    return moves;
}

bool Rook::canMoveTo(const Position &from, const Position &to, const ChessBoard &board) const
{
    // Must be horizontal or vertical move
    if (from.rank != to.rank && from.file != to.file)
    {
        return false;
    }

    // Path must be clear
    if (!board.isPathClear(from, to))
    {
        return false;
    }

    // Destination must be empty or contain enemy piece
    return board.isSquareEmpty(to) || board.isSquareOccupiedBy(to, oppositeColor(color_));
}

// Knight implementation
std::vector<Position> Knight::getPossibleMoves(const Position &from, const ChessBoard &board) const
{
    std::vector<Position> moves;

    // Knight moves: 2 squares in one direction, 1 in perpendicular
    int knightMoves[8][2] = {
        {2, 1}, {2, -1}, {-2, 1}, {-2, -1}, {1, 2}, {1, -2}, {-1, 2}, {-1, -2}};

    for (int i = 0; i < 8; ++i)
    {
        Position target(from.rank + knightMoves[i][0], from.file + knightMoves[i][1]);
        if (target.isValid() &&
            (board.isSquareEmpty(target) || board.isSquareOccupiedBy(target, oppositeColor(color_))))
        {
            moves.push_back(target);
        }
    }

    return moves;
}

bool Knight::canMoveTo(const Position &from, const Position &to, const ChessBoard &board) const
{
    int rankDiff = std::abs(to.rank - from.rank);
    int fileDiff = std::abs(to.file - from.file);

    // Knight moves in L-shape
    if (!((rankDiff == 2 && fileDiff == 1) || (rankDiff == 1 && fileDiff == 2)))
    {
        return false;
    }

    // Destination must be empty or contain enemy piece
    return board.isSquareEmpty(to) || board.isSquareOccupiedBy(to, oppositeColor(color_));
}

// Bishop implementation
std::vector<Position> Bishop::getPossibleMoves(const Position &from, const ChessBoard &board) const
{
    std::vector<Position> moves;

    // Diagonal directions
    int directions[4][2] = {{1, 1}, {1, -1}, {-1, 1}, {-1, -1}};

    for (int i = 0; i < 4; ++i)
    {
        int rankStep = directions[i][0];
        int fileStep = directions[i][1];

        Position current(from.rank + rankStep, from.file + fileStep);
        while (current.isValid())
        {
            if (board.isSquareEmpty(current))
            {
                moves.push_back(current);
            }
            else
            {
                if (board.isSquareOccupiedBy(current, oppositeColor(color_)))
                {
                    moves.push_back(current);
                }
                break; // Stop at any piece
            }
            current.rank += rankStep;
            current.file += fileStep;
        }
    }

    return moves;
}

bool Bishop::canMoveTo(const Position &from, const Position &to, const ChessBoard &board) const
{
    int rankDiff = std::abs(to.rank - from.rank);
    int fileDiff = std::abs(to.file - from.file);

    // Must be diagonal move
    if (rankDiff != fileDiff)
    {
        return false;
    }

    // Path must be clear
    if (!board.isPathClear(from, to))
    {
        return false;
    }

    // Destination must be empty or contain enemy piece
    return board.isSquareEmpty(to) || board.isSquareOccupiedBy(to, oppositeColor(color_));
}

// Queen implementation (combines Rook and Bishop moves)
std::vector<Position> Queen::getPossibleMoves(const Position &from, const ChessBoard &board) const
{
    std::vector<Position> moves;

    // All 8 directions (horizontal, vertical, diagonal)
    int directions[8][2] = {
        {0, 1}, {0, -1}, {1, 0}, {-1, 0}, // Rook moves
        {1, 1},
        {1, -1},
        {-1, 1},
        {-1, -1} // Bishop moves
    };

    for (int i = 0; i < 8; ++i)
    {
        int rankStep = directions[i][0];
        int fileStep = directions[i][1];

        Position current(from.rank + rankStep, from.file + fileStep);
        while (current.isValid())
        {
            if (board.isSquareEmpty(current))
            {
                moves.push_back(current);
            }
            else
            {
                if (board.isSquareOccupiedBy(current, oppositeColor(color_)))
                {
                    moves.push_back(current);
                }
                break; // Stop at any piece
            }
            current.rank += rankStep;
            current.file += fileStep;
        }
    }

    return moves;
}

bool Queen::canMoveTo(const Position &from, const Position &to, const ChessBoard &board) const
{
    int rankDiff = std::abs(to.rank - from.rank);
    int fileDiff = std::abs(to.file - from.file);

    // Must be horizontal, vertical, or diagonal move
    if (!(from.rank == to.rank || from.file == to.file || rankDiff == fileDiff))
    {
        return false;
    }

    // Path must be clear
    if (!board.isPathClear(from, to))
    {
        return false;
    }

    // Destination must be empty or contain enemy piece
    return board.isSquareEmpty(to) || board.isSquareOccupiedBy(to, oppositeColor(color_));
}

// King implementation
std::vector<Position> King::getPossibleMoves(const Position &from, const ChessBoard &board) const
{
    std::vector<Position> moves;

    // King moves one square in any direction
    int directions[8][2] = {
        {0, 1}, {0, -1}, {1, 0}, {-1, 0}, {1, 1}, {1, -1}, {-1, 1}, {-1, -1}};

    for (int i = 0; i < 8; ++i)
    {
        Position target(from.rank + directions[i][0], from.file + directions[i][1]);
        if (target.isValid() &&
            (board.isSquareEmpty(target) || board.isSquareOccupiedBy(target, oppositeColor(color_))) &&
            !board.isSquareAttackedBy(target, oppositeColor(color_)))
        {
            moves.push_back(target);
        }
    }

    // Castling moves
    if (!hasMoved_ && !board.isInCheck(color_))
    {
        // Kingside castling
        if (board.canCastleKingside(color_))
        {
            Position rookPos(from.rank, 7);
            if (board.isSquareEmpty(Position(from.rank, 5)) &&
                board.isSquareEmpty(Position(from.rank, 6)) &&
                !board.isSquareAttackedBy(Position(from.rank, 5), oppositeColor(color_)) &&
                !board.isSquareAttackedBy(Position(from.rank, 6), oppositeColor(color_)))
            {
                moves.push_back(Position(from.rank, 6));
            }
        }

        // Queenside castling
        if (board.canCastleQueenside(color_))
        {
            Position rookPos(from.rank, 0);
            if (board.isSquareEmpty(Position(from.rank, 1)) &&
                board.isSquareEmpty(Position(from.rank, 2)) &&
                board.isSquareEmpty(Position(from.rank, 3)) &&
                !board.isSquareAttackedBy(Position(from.rank, 2), oppositeColor(color_)) &&
                !board.isSquareAttackedBy(Position(from.rank, 3), oppositeColor(color_)))
            {
                moves.push_back(Position(from.rank, 2));
            }
        }
    }

    return moves;
}

bool King::canMoveTo(const Position &from, const Position &to, const ChessBoard &board) const
{
    int rankDiff = std::abs(to.rank - from.rank);
    int fileDiff = std::abs(to.file - from.file);

    // Normal king move (one square in any direction)
    if (rankDiff <= 1 && fileDiff <= 1 && (rankDiff != 0 || fileDiff != 0))
    {
        return (board.isSquareEmpty(to) || board.isSquareOccupiedBy(to, oppositeColor(color_))) &&
               !board.isSquareAttackedBy(to, oppositeColor(color_));
    }

    // Castling moves
    if (rankDiff == 0 && fileDiff == 2 && !hasMoved_ && !board.isInCheck(color_))
    {
        if (to.file == 6)
        { // Kingside castling
            return board.canCastleKingside(color_) &&
                   board.isSquareEmpty(Position(from.rank, 5)) &&
                   board.isSquareEmpty(Position(from.rank, 6)) &&
                   !board.isSquareAttackedBy(Position(from.rank, 5), oppositeColor(color_)) &&
                   !board.isSquareAttackedBy(Position(from.rank, 6), oppositeColor(color_));
        }
        else if (to.file == 2)
        { // Queenside castling
            return board.canCastleQueenside(color_) &&
                   board.isSquareEmpty(Position(from.rank, 1)) &&
                   board.isSquareEmpty(Position(from.rank, 2)) &&
                   board.isSquareEmpty(Position(from.rank, 3)) &&
                   !board.isSquareAttackedBy(Position(from.rank, 2), oppositeColor(color_)) &&
                   !board.isSquareAttackedBy(Position(from.rank, 3), oppositeColor(color_));
        }
    }

    return false;
}

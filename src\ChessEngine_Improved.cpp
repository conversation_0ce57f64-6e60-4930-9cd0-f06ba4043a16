#include "ChessEngine.h"
#include "ImprovedPieceSquareTables.h"
#include "ZobristHash.h"
#include "TacticalPatterns.h"
#include "ImprovedSearch.h"
#include <algorithm>
#include <random>
#include <chrono>
#include <iostream>
#include <thread>

ChessEngine::ChessEngine(Color color, int depth, int threads)
    : maxDepth_(depth), engineColor_(color), searchDepth_(0), searchInProgress_(false),
      quietMode_(false), uciInfoCallback_(nullptr), bestMoveCallback_(nullptr),
      nodesSearched_(0), ttHits_(0), nullMoveCutoffs_(0), lmrReductions_(0), multiCutPrunings_(0),
      tbHits_(0), totalNodesSearched_(0), totalTbHits_(0), threadPool_(nullptr), stopSearch_(false),
      allowNullMove_(true), nullMoveReduction_(2), lmrThreshold_(3), lmrReduction_(1),
      syzygyProbeDepth_(1)
{
    transpositionTable_.reserve(TT_SIZE);

    // Initialize move ordering tables
    for (int i = 0; i < 64; ++i)
    {
        killerMoves_[i][0] = Move();
        killerMoves_[i][1] = Move();
        principalVariation_[i] = Move();

        for (int j = 0; j < 64; ++j)
        {
            historyTable_[i][j] = 0;
        }
    }

    // Initialize thread pool if threads > 1
    if (threads > 1)
    {
        setThreadCount(threads);
    }
}

void ChessEngine::setThreadCount(int threads)
{
    // Clean up existing thread pool
    if (threadPool_)
    {
        delete threadPool_;
        threadPool_ = nullptr;
    }

    // Create new thread pool if threads > 1
    if (threads > 1)
    {
        threadPool_ = new ThreadPool(threads);
    }
}

void ChessEngine::setTTSize(size_t mbSize)
{
    // Calculate number of entries based on MB size
    // Each entry is approximately 32 bytes
    size_t entries = (mbSize * 1024 * 1024) / 32;

    // Clear and resize transposition table
    transpositionTable_.clear();
    transpositionTable_.reserve(entries);
}

bool ChessEngine::initializeSyzygyTablebase(const std::vector<std::string> &paths, int maxPieces)
{
    return syzygyTablebase_.initialize(paths, maxPieces);
}

Move ChessEngine::getBestMove(const ChessGame &game)
{
    // Default time: 5 seconds
    return getBestMoveWithTime(game, 5000);
}

Move ChessEngine::getBestMoveWithTime(const ChessGame &game, int timeMs)
{
    // Set up time control
    TimeManager::TimeControl tc;
    tc.moveTime = timeMs;

    return getBestMoveWithTimeControl(game, tc);
}

Move ChessEngine::getBestMoveWithTimeControl(const ChessGame &game, const TimeManager::TimeControl &tc)
{
    // Check for opening book move first
    if (hasOpeningBook())
    {
        Move bookMove = getOpeningMove(game);
        if (bookMove.isValid())
        {
            if (!quietMode_)
            {
                std::cout << "Book move: " << bookMove.toAlgebraic() << std::endl;
            }
            return bookMove;
        }
    }

    // Set up time control
    timeManager_.setSideToMove(game.getCurrentPlayer());
    timeManager_.setTimeControl(game.getCurrentPlayer(), tc);

    if (tc.moveTime > 0)
    {
        timeManager_.setMode(TimeManager::Mode::FIXED_TIME);
    }
    else if (tc.timeMs > 0)
    {
        timeManager_.setMode(TimeManager::Mode::TOURNAMENT);
    }
    else
    {
        timeManager_.setMode(TimeManager::Mode::FIXED_DEPTH);
    }

    // Calculate time allocation
    timeManager_.calculateTimeForMove();

    // Start the clock
    timeManager_.startClock();

    // Reset search statistics
    resetSearchStats();

    // Create a copy of the game for searching
    ChessGame gameCopy = game;

    // If multi-threading is enabled, use thread pool
    if (threadPool_ && threadPool_->size() > 1)
    {
        // Root-parallel search with Lazy SMP
        stopSearch_ = false;
        searchInProgress_ = true;
        searchDepth_ = maxDepth_;

        int bestScore = 0;
        Move bestMove = searchRootParallel(gameCopy, maxDepth_, bestScore);

        // Wait for all tasks to finish
        threadPool_->waitForTasks();
        searchInProgress_ = false;
        // Stop all threads
        stopSearch_ = true;

        // Wait for all threads to finish
        if (threadPool_)
        {
            threadPool_->waitForTasks();
        }

        searchInProgress_ = false;

        // Return best move
        return bestMove;
    }
    else
    {
        // Single-threaded search
        return iterativeDeepening(gameCopy, maxDepth_);
    }
}

void ChessEngine::startSearch(const ChessGame &game)
{
    // Don't start if already searching
    if (searchInProgress_)
    {
        return;
    }

    // Start asynchronous search
    searchInProgress_ = true;
    stopSearch_ = false;

    // Create a copy of the game for searching
    ChessGame gameCopy = game;

    // Launch search in a separate thread
    std::thread searchThread([this, gameCopy]() mutable
                             {
        // Check for opening book move first
        if (hasOpeningBook()) {
            Move bookMove = getOpeningMove(gameCopy);
            if (bookMove.isValid()) {
                if (bestMoveCallback_) {
                    bestMoveCallback_(bookMove);
                }
                searchInProgress_ = false;
                return;
            }
        }

        // Reset search statistics
        resetSearchStats();

        // Start the clock
        timeManager_.startClock();

        // Perform iterative deepening search
        Move bestMove = iterativeDeepening(gameCopy, maxDepth_);

        // Call the callback with the best move
        if (bestMoveCallback_) {
            bestMoveCallback_(bestMove);
        }

        searchInProgress_ = false; });

    // Detach the thread to let it run independently
    searchThread.detach();
}

void ChessEngine::stopSearch()
{
    stopSearch_ = true;
}

void ChessEngine::searchThreadMain(ChessGame game, int depth, int threadId)
{
    // Helper threads perform search with different parameters
    // to explore different parts of the search tree

    // Adjust search parameters for this thread
    int alpha = -std::numeric_limits<int>::max();
    int beta = std::numeric_limits<int>::max();

    // Different threads can use different search parameters
    // to increase search diversity
    if (threadId % 3 == 1)
    {
        // This thread focuses on aggressive moves
        // by slightly modifying evaluation weights
    }
    else if (threadId % 3 == 2)
    {
        // This thread focuses on positional moves
    }

    // Perform iterative deepening up to the specified depth
    for (int d = 1; d <= depth && !stopSearch_; ++d)
    {
        // Perform principal variation search
        principalVariationSearch(game, d, alpha, beta, true);
    }
}

Move ChessEngine::iterativeDeepening(ChessGame &game, int maxDepth)
{
    Move bestMove;
    int bestScore = -std::numeric_limits<int>::max();
    currentPV_.clear();
    bestPVLine_.clear();

    // --- Syzygy tablebase root probing ----------------------------------
    if (syzygyProbeDepth_ > 0 && syzygyTablebase_.isInitialized())
    {
        if (maxDepth >= syzygyProbeDepth_)
        {
            std::vector<Move> tbMoves;
            SyzygyTablebase::ProbeData tbData;
            if (syzygyTablebase_.probeRoot(game.getBoard(), game.getCurrentPlayer(), tbMoves, tbData) && !tbMoves.empty())
            {
                // Choose the best tablebase move (first move returned is best)
                return tbMoves[0];
            }
        }
    }
    // --------------------------------------------------------------------

    // For aspiration windows
    int alpha = -std::numeric_limits<int>::max();
    int beta = std::numeric_limits<int>::max();
    int delta = 25; // Initial window size

    // Iterative deepening
    for (int depth = 1; depth <= maxDepth; ++depth)
    {
        // Check if time is up before starting a new iteration
        if (timeManager_.isTimeUp())
        {
            break;
        }

        // Use aspiration windows for depths > 3
        if (depth > 3 && bestScore != -std::numeric_limits<int>::max())
        {
            alpha = bestScore - delta;
            beta = bestScore + delta;
        }

        int score;
        bool windowFailed = false;

        do
        {
            // Reset PV for this iteration
            currentPV_.clear();

            // Perform principal variation search
            score = principalVariationSearch(game, depth, alpha, beta, true);

            // Check if we failed low or high
            if (score <= alpha)
            {
                // Failed low, widen window
                alpha = std::max(-std::numeric_limits<int>::max(), alpha - delta);
                windowFailed = true;
            }
            else if (score >= beta)
            {
                // Failed high, widen window
                beta = std::min(std::numeric_limits<int>::max(), beta + delta);
                windowFailed = true;
            }
            else
            {
                // Window is good
                windowFailed = false;

                // Update best score and move
                bestScore = score;
                if (!currentPV_.empty())
                {
                    bestMove = currentPV_[0];
                    bestPVLine_ = currentPV_;
                }
            }

            // Increase window size for next attempt
            delta *= 2;

            // Check if time is up after a search iteration
            if (timeManager_.isTimeUp())
            {
                break;
            }

        } while (windowFailed);

        // Check if time is up after completing a depth
        if (timeManager_.isTimeUp())
        {
            break;
        }

        // Send UCI info if callback is set
        if (uciInfoCallback_ && !quietMode_)
        {
            std::string pvString = getPVString();
            std::string info = "info depth " + std::to_string(depth) +
                               " score cp " + std::to_string(bestScore) +
                               " nodes " + std::to_string(totalNodesSearched_) +
                               " time " + std::to_string(timeManager_.getElapsedTimeMs()) +
                               " pv " + pvString;
            uciInfoCallback_(info);
        }

        // Check if we should stop search based on time management
        if (timeManager_.shouldStopSearch() && depth >= 5)
        {
            // We have a reasonable depth and should stop to save time
            break;
        }
    }

    // Record the time used for this move
    timeManager_.recordMoveTime(timeManager_.getElapsedTimeMs());

    // If no valid move was found, try to find any legal move
    if (!bestMove.isValid())
    {
        std::vector<Move> legalMoves = game.getAllValidMoves();
        if (!legalMoves.empty())
        {
            bestMove = legalMoves[0];
        }
    }

    return bestMove;
}

void ChessEngine::resetSearchStats()
{
    nodesSearched_ = 0;
    ttHits_ = 0;
    nullMoveCutoffs_ = 0;
    lmrReductions_ = 0;
    multiCutPrunings_ = 0;
    tbHits_ = 0;
    totalNodesSearched_ = 0;
    totalTbHits_ = 0;

    // Reset thread-specific data if thread pool exists
    if (threadPool_)
    {
        threadPool_->resetThreadData();
    }
}

void ChessEngine::lazyInit()
{
    // Initialize thread pool if not already initialized
    if (!threadPool_ && getThreadCount() > 1)
    {
        threadPool_ = new ThreadPool(getThreadCount());
    }
}
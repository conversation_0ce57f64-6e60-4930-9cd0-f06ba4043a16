#include "ChessEngine.h"
#include "ImprovedPieceSquareTables.h"
#include "ZobristHash.h"
#include "TacticalPatterns.h"
#include "ImprovedSearch.h"
#include <algorithm>
#include <random>
#include <chrono>
#include <iostream>
#include <thread>

ChessEngine::ChessEngine(Color color, int depth, int threads)
    : maxDepth_(depth), engineColor_(color), searchDepth_(0), searchInProgress_(false),
      quietMode_(false), uciInfoCallback_(nullptr), bestMoveCallback_(nullptr),
      nodesSearched_(0), ttHits_(0), nullMoveCutoffs_(0), lmrReductions_(0), multiCutPrunings_(0),
      tbHits_(0), totalNodesSearched_(0), totalTbHits_(0), threadPool_(nullptr), stopSearch_(false),
      allowNullMove_(true), nullMoveReduction_(2), lmrThreshold_(3), lmrReduction_(1),
      syzygyProbeDepth_(1)
{
    transpositionTable_.reserve(TT_SIZE);

    // Initialize move ordering tables
    for (int i = 0; i < 64; ++i)
    {
        killerMoves_[i][0] = Move();
        killerMoves_[i][1] = Move();
        principalVariation_[i] = Move();

        for (int j = 0; j < 64; ++j)
        {
            historyTable_[i][j] = 0;
        }
    }

    // Initialize thread pool if threads > 1
    if (threads > 1)
    {
        setThreadCount(threads);
    }
}

void ChessEngine::setThreadCount(int threads)
{
    // Clean up existing thread pool
    if (threadPool_)
    {
        delete threadPool_;
        threadPool_ = nullptr;
    }

    // Create new thread pool if threads > 1
    if (threads > 1)
    {
        threadPool_ = new ThreadPool(threads);
    }
}

void ChessEngine::setTTSize(size_t mbSize)
{
    // Calculate number of entries based on MB size
    // Each entry is approximately 32 bytes
    size_t entries = (mbSize * 1024 * 1024) / 32;

    // Clear and resize transposition table
    transpositionTable_.clear();
    transpositionTable_.reserve(entries);
}

bool ChessEngine::initializeSyzygyTablebase(const std::vector<std::string> &paths, int maxPieces)
{
    return syzygyTablebase_.initialize(paths, maxPieces);
}

Move ChessEngine::getBestMove(const ChessGame &game)
{
    // Default time: 5 seconds
    return getBestMoveWithTime(game, 5000);
}

Move ChessEngine::getBestMoveWithTime(const ChessGame &game, int timeMs)
{
    // Set up time control
    TimeManager::TimeControl tc;
    tc.moveTime = timeMs;

    return getBestMoveWithTimeControl(game, tc);
}

Move ChessEngine::getBestMoveWithTimeControl(const ChessGame &game, const TimeManager::TimeControl &tc)
{
    // Check for opening book move first
    if (hasOpeningBook())
    {
        Move bookMove = getOpeningMove(game);
        if (bookMove.isValid())
        {
            if (!quietMode_)
            {
                std::cout << "Book move: " << bookMove.toAlgebraic() << std::endl;
            }
            return bookMove;
        }
    }

    // Set up time control
    timeManager_.setSideToMove(game.getCurrentPlayer());
    timeManager_.setTimeControl(game.getCurrentPlayer(), tc);

    if (tc.moveTime > 0)
    {
        timeManager_.setMode(TimeManager::Mode::FIXED_TIME);
    }
    else if (tc.timeMs > 0)
    {
        timeManager_.setMode(TimeManager::Mode::TOURNAMENT);
    }
    else
    {
        timeManager_.setMode(TimeManager::Mode::FIXED_DEPTH);
    }

    // Calculate time allocation
    timeManager_.calculateTimeForMove();

    // Start the clock
    timeManager_.startClock();

    // Reset search statistics
    resetSearchStats();

    // Create a copy of the game for searching
    ChessGame gameCopy = game;

    // If multi-threading is enabled, use thread pool
    if (threadPool_ && threadPool_->size() > 1)
    {
        // Root-parallel search with Lazy SMP
        stopSearch_ = false;
        searchInProgress_ = true;
        searchDepth_ = maxDepth_;

        int bestScore = 0;
        Move bestMove = searchRootParallel(gameCopy, maxDepth_, bestScore);

        // Wait for all tasks to finish
        threadPool_->waitForTasks();
        searchInProgress_ = false;
        // Stop all threads
        stopSearch_ = true;

        // Wait for all threads to finish
        if (threadPool_)
        {
            threadPool_->waitForTasks();
        }

        searchInProgress_ = false;

        // Return best move
        return bestMove;
    }
    else
    {
        // Single-threaded search
        return iterativeDeepening(gameCopy, maxDepth_);
    }
}

void ChessEngine::startSearch(const ChessGame &game)
{
    // Don't start if already searching
    if (searchInProgress_)
    {
        return;
    }

    // Start asynchronous search
    searchInProgress_ = true;
    stopSearch_ = false;

    // Create a copy of the game for searching
    ChessGame gameCopy = game;

    // Launch search in a separate thread
    std::thread searchThread([this, gameCopy]() mutable
                             {
        // Check for opening book move first
        if (hasOpeningBook()) {
            Move bookMove = getOpeningMove(gameCopy);
            if (bookMove.isValid()) {
                if (bestMoveCallback_) {
                    bestMoveCallback_(bookMove);
                }
                searchInProgress_ = false;
                return;
            }
        }

        // Reset search statistics
        resetSearchStats();

        // Start the clock
        timeManager_.startClock();

        // Perform iterative deepening search
        Move bestMove = iterativeDeepening(gameCopy, maxDepth_);

        // Call the callback with the best move
        if (bestMoveCallback_) {
            bestMoveCallback_(bestMove);
        }

        searchInProgress_ = false; });

    // Detach the thread to let it run independently
    searchThread.detach();
}

void ChessEngine::stopSearch()
{
    stopSearch_ = true;
}

void ChessEngine::searchThreadMain(ChessGame game, int depth, int threadId)
{
    // Helper threads perform search with different parameters
    // to explore different parts of the search tree

    // Adjust search parameters for this thread
    int alpha = -std::numeric_limits<int>::max();
    int beta = std::numeric_limits<int>::max();

    // Different threads can use different search parameters
    // to increase search diversity
    if (threadId % 3 == 1)
    {
        // This thread focuses on aggressive moves
        // by slightly modifying evaluation weights
    }
    else if (threadId % 3 == 2)
    {
        // This thread focuses on positional moves
    }

    // Perform iterative deepening up to the specified depth
    for (int d = 1; d <= depth && !stopSearch_; ++d)
    {
        // Perform principal variation search
        principalVariationSearch(game, d, alpha, beta, true);
    }
}

Move ChessEngine::iterativeDeepening(ChessGame &game, int maxDepth)
{
    Move bestMove;
    int bestScore = -std::numeric_limits<int>::max();
    currentPV_.clear();
    bestPVLine_.clear();

    // --- Syzygy tablebase root probing ----------------------------------
    if (syzygyProbeDepth_ > 0 && syzygyTablebase_.isInitialized())
    {
        if (maxDepth >= syzygyProbeDepth_)
        {
            std::vector<Move> tbMoves;
            SyzygyTablebase::ProbeData tbData;
            if (syzygyTablebase_.probeRoot(game.getBoard(), game.getCurrentPlayer(), tbMoves, tbData) && !tbMoves.empty())
            {
                // Choose the best tablebase move (first move returned is best)
                return tbMoves[0];
            }
        }
    }
    // --------------------------------------------------------------------

    // For aspiration windows
    int alpha = -std::numeric_limits<int>::max();
    int beta = std::numeric_limits<int>::max();
    int delta = 25; // Initial window size

    // Iterative deepening
    for (int depth = 1; depth <= maxDepth; ++depth)
    {
        // Check if time is up before starting a new iteration
        if (timeManager_.isTimeUp())
        {
            break;
        }

        // Use aspiration windows for depths > 3
        if (depth > 3 && bestScore != -std::numeric_limits<int>::max())
        {
            alpha = bestScore - delta;
            beta = bestScore + delta;
        }

        int score;
        bool windowFailed = false;

        do
        {
            // Reset PV for this iteration
            currentPV_.clear();

            // Perform principal variation search
            score = principalVariationSearch(game, depth, alpha, beta, true);

            // Check if we failed low or high
            if (score <= alpha)
            {
                // Failed low, widen window
                alpha = std::max(-std::numeric_limits<int>::max(), alpha - delta);
                windowFailed = true;
            }
            else if (score >= beta)
            {
                // Failed high, widen window
                beta = std::min(std::numeric_limits<int>::max(), beta + delta);
                windowFailed = true;
            }
            else
            {
                // Window is good
                windowFailed = false;

                // Update best score and move
                bestScore = score;
                if (!currentPV_.empty())
                {
                    bestMove = currentPV_[0];
                    bestPVLine_ = currentPV_;
                }
            }

            // Increase window size for next attempt
            delta *= 2;

            // Check if time is up after a search iteration
            if (timeManager_.isTimeUp())
            {
                break;
            }

        } while (windowFailed);

        // Check if time is up after completing a depth
        if (timeManager_.isTimeUp())
        {
            break;
        }

        // Send UCI info if callback is set
        if (uciInfoCallback_ && !quietMode_)
        {
            std::string pvString = getPVString();
            std::string info = "info depth " + std::to_string(depth) +
                               " score cp " + std::to_string(bestScore) +
                               " nodes " + std::to_string(totalNodesSearched_) +
                               " time " + std::to_string(timeManager_.getElapsedTimeMs()) +
                               " pv " + pvString;
            uciInfoCallback_(info);
        }

        // Check if we should stop search based on time management
        if (timeManager_.shouldStopSearch() && depth >= 5)
        {
            // We have a reasonable depth and should stop to save time
            break;
        }
    }

    // Record the time used for this move
    timeManager_.recordMoveTime(timeManager_.getElapsedTimeMs());

    // If no valid move was found, try to find any legal move
    if (!bestMove.isValid())
    {
        std::vector<Move> legalMoves = game.getAllValidMoves();
        if (!legalMoves.empty())
        {
            bestMove = legalMoves[0];
        }
    }

    return bestMove;
}

void ChessEngine::resetSearchStats()
{
    nodesSearched_ = 0;
    ttHits_ = 0;
    nullMoveCutoffs_ = 0;
    lmrReductions_ = 0;
    multiCutPrunings_ = 0;
    tbHits_ = 0;
    totalNodesSearched_ = 0;
    totalTbHits_ = 0;

    // Reset thread-specific data if thread pool exists
    if (threadPool_)
    {
        threadPool_->resetThreadData();
    }
}

void ChessEngine::lazyInit()
{
    // Initialize thread pool if not already initialized
    if (!threadPool_ && getThreadCount() > 1)
    {
        threadPool_ = new ThreadPool(getThreadCount());
    }
}

// Missing method implementations

int ChessEngine::principalVariationSearch(ChessGame &game, int depth, int alpha, int beta, bool maximizingPlayer)
{
    // Simple implementation - can be improved later
    return minimax(game, depth, alpha, beta, maximizingPlayer);
}

int ChessEngine::minimax(ChessGame &game, int depth, int alpha, int beta, bool maximizingPlayer)
{
    totalNodesSearched_++;
    nodesSearched_++;

    // Base case: if depth is 0 or game is over
    if (depth == 0 || game.isCheckmate() || game.isStalemate())
    {
        return evaluatePosition(game.getBoard(), maximizingPlayer ? engineColor_ : oppositeColor(engineColor_));
    }

    // Check for time limit
    if (timeManager_.isTimeUp())
    {
        return evaluatePosition(game.getBoard(), maximizingPlayer ? engineColor_ : oppositeColor(engineColor_));
    }

    std::vector<Move> moves = game.getAllValidMoves();
    if (moves.empty())
    {
        if (game.isInCheck())
        {
            // Checkmate
            return maximizingPlayer ? -9999 + (maxDepth_ - depth) : 9999 - (maxDepth_ - depth);
        }
        else
        {
            // Stalemate
            return 0;
        }
    }

    // Order moves for better alpha-beta pruning
    moves = orderMoves(moves, game);

    if (maximizingPlayer)
    {
        int maxEval = -std::numeric_limits<int>::max();
        for (const Move &move : moves)
        {
            if (timeManager_.isTimeUp())
                break;

            game.makeMove(move);
            int eval = minimax(game, depth - 1, alpha, beta, false);
            game.undoLastMove();

            maxEval = std::max(maxEval, eval);
            alpha = std::max(alpha, eval);

            if (beta <= alpha)
            {
                break; // Alpha-beta pruning
            }
        }
        return maxEval;
    }
    else
    {
        int minEval = std::numeric_limits<int>::max();
        for (const Move &move : moves)
        {
            if (timeManager_.isTimeUp())
                break;

            game.makeMove(move);
            int eval = minimax(game, depth - 1, alpha, beta, true);
            game.undoLastMove();

            minEval = std::min(minEval, eval);
            beta = std::min(beta, eval);

            if (beta <= alpha)
            {
                break; // Alpha-beta pruning
            }
        }
        return minEval;
    }
}

std::string ChessEngine::getPVString(int maxMoves) const
{
    std::string pvString;
    int count = 0;

    for (const Move &move : bestPVLine_)
    {
        if (count >= maxMoves)
            break;
        if (count > 0)
            pvString += " ";
        pvString += move.toAlgebraic();
        count++;
    }

    return pvString;
}

Move ChessEngine::getOpeningMove(const ChessGame &game) const
{
    if (improvedOpeningBook_.isLoaded())
    {
        // Cast away const to call non-const method
        ImprovedOpeningBook &book = const_cast<ImprovedOpeningBook &>(improvedOpeningBook_);
        return book.getBookMove(game.getBoard(), game.getCurrentPlayer(), 0, -1);
    }
    else if (openingBook_.isLoaded())
    {
        return openingBook_.getBookMove(game);
    }

    return Move(); // Invalid move if no book available
}

std::string ChessEngine::getAnalysisString() const
{
    std::string analysis = "Nodes: " + std::to_string(totalNodesSearched_);
    analysis += ", TT Hits: " + std::to_string(ttHits_);
    analysis += ", TB Hits: " + std::to_string(totalTbHits_);

    if (!bestPVLine_.empty())
    {
        analysis += ", PV: " + getPVString(5);
    }

    return analysis;
}

int ChessEngine::evaluatePosition(const ChessBoard &board, Color perspective) const
{
    int score = 0;

    // Material evaluation
    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            Position pos(rank, file);
            const Piece *piece = board.getPiece(pos);
            if (piece)
            {
                int pieceValue = 0;
                switch (piece->getType())
                {
                case PieceType::PAWN:
                    pieceValue = 100;
                    break;
                case PieceType::KNIGHT:
                    pieceValue = 320;
                    break;
                case PieceType::BISHOP:
                    pieceValue = 330;
                    break;
                case PieceType::ROOK:
                    pieceValue = 500;
                    break;
                case PieceType::QUEEN:
                    pieceValue = 900;
                    break;
                case PieceType::KING:
                    pieceValue = 20000;
                    break;
                default:
                    pieceValue = 0;
                    break;
                }

                if (piece->getColor() == perspective)
                {
                    score += pieceValue;
                }
                else
                {
                    score -= pieceValue;
                }
            }
        }
    }

    // Add positional bonuses (simplified)
    // Center control
    for (int rank = 3; rank <= 4; ++rank)
    {
        for (int file = 3; file <= 4; ++file)
        {
            Position pos(rank, file);
            const Piece *piece = board.getPiece(pos);
            if (piece)
            {
                int bonus = 10;
                if (piece->getColor() == perspective)
                {
                    score += bonus;
                }
                else
                {
                    score -= bonus;
                }
            }
        }
    }

    return score;
}

std::vector<Move> ChessEngine::orderMoves(const std::vector<Move> &moves, const ChessGame &game) const
{
    std::vector<std::pair<Move, int>> scoredMoves;

    for (const Move &move : moves)
    {
        int score = 0;

        // Prioritize captures
        const Piece *capturedPiece = game.getBoard().getPiece(move.getTo());
        if (capturedPiece)
        {
            // MVV-LVA (Most Valuable Victim - Least Valuable Attacker)
            int victimValue = 0;
            int attackerValue = 0;

            switch (capturedPiece->getType())
            {
            case PieceType::PAWN:
                victimValue = 100;
                break;
            case PieceType::KNIGHT:
                victimValue = 320;
                break;
            case PieceType::BISHOP:
                victimValue = 330;
                break;
            case PieceType::ROOK:
                victimValue = 500;
                break;
            case PieceType::QUEEN:
                victimValue = 900;
                break;
            default:
                victimValue = 0;
                break;
            }

            const Piece *attackerPiece = game.getBoard().getPiece(move.getFrom());
            if (attackerPiece)
            {
                switch (attackerPiece->getType())
                {
                case PieceType::PAWN:
                    attackerValue = 100;
                    break;
                case PieceType::KNIGHT:
                    attackerValue = 320;
                    break;
                case PieceType::BISHOP:
                    attackerValue = 330;
                    break;
                case PieceType::ROOK:
                    attackerValue = 500;
                    break;
                case PieceType::QUEEN:
                    attackerValue = 900;
                    break;
                default:
                    attackerValue = 0;
                    break;
                }
            }

            score += victimValue - attackerValue / 10;
        }

        // Add some randomness to avoid repetition
        score += rand() % 10;

        scoredMoves.push_back({move, score});
    }

    // Sort moves by score (highest first)
    std::sort(scoredMoves.begin(), scoredMoves.end(),
              [](const std::pair<Move, int> &a, const std::pair<Move, int> &b)
              {
                  return a.second > b.second;
              });

    std::vector<Move> orderedMoves;
    for (const auto &scoredMove : scoredMoves)
    {
        orderedMoves.push_back(scoredMove.first);
    }

    return orderedMoves;
}
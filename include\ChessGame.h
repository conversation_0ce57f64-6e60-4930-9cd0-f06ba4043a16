#pragma once

#include "ChessBoard.h"
#include "Move.h"
#include <vector>
#include <string>

enum class GameState {
    PLAYING,
    CHECK,
    CHECKMATE,
    STALEMATE,
    DRAW
};

class ChessGame {
private:
    ChessBoard board_;
    Color currentPlayer_;
    GameState gameState_;
    std::vector<Move> moveHistory_;
    int halfMoveClock_;  // For 50-move rule
    int fullMoveNumber_;
    
public:
    ChessGame();
    
    // Game control
    void startNewGame();
    bool makeMove(const Move& move);
    bool makeMove(const std::string& algebraicMove);
    void undoLastMove();
    
    // Game state queries
    Color getCurrentPlayer() const { return currentPlayer_; }
    GameState getGameState() const { return gameState_; }
    const ChessBoard& getBoard() const { return board_; }
    ChessBoard& getBoard() { return board_; }
    
    // Move validation
    bool isValidMove(const Move& move) const;
    std::vector<Move> getAllValidMoves() const;
    std::vector<Move> getAllValidMovesForPiece(const Position& pos) const;
    
    // Game status
    bool isInCheck() const;
    bool isCheckmate() const;
    bool isStalemate() const;
    bool isDraw() const;
    
    // Move history
    const std::vector<Move>& getMoveHistory() const { return moveHistory_; }
    std::string getLastMoveString() const;
    
    // Display
    void displayBoard() const;
    std::string getBoardString() const;
    std::string getGameStatusString() const;
    
    // Utility
    void switchPlayer();
    
private:
    void updateGameState();
    bool wouldBeInCheckAfterMove(const Move& move) const;
    void handleSpecialMoves(const Move& move);
    bool isThreefoldRepetition() const;
    bool isFiftyMoveRule() const;
};

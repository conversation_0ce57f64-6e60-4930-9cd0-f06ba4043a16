#include "ChessBoard.h"
#include "Pieces.h"
#include <iostream>
#include <sstream>

ChessBoard::ChessBoard()
{
    clearBoard();
    setupInitialPosition();
}

ChessBoard::ChessBoard(const ChessBoard &other)
{
    copyFrom(other);
}

ChessBoard &ChessBoard::operator=(const ChessBoard &other)
{
    if (this != &other)
    {
        copyFrom(other);
    }
    return *this;
}

void ChessBoard::copyFrom(const ChessBoard &other)
{
    // Clear current board
    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            board_[rank][file].reset();
        }
    }

    // Copy pieces
    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            if (other.board_[rank][file])
            {
                board_[rank][file] = other.board_[rank][file]->clone();
            }
        }
    }

    // Copy game state
    enPassantTarget_ = other.enPassantTarget_;
    whiteCanCastleKingside_ = other.whiteCanCastleKingside_;
    whiteCanCastleQueenside_ = other.whiteCanCastleQueenside_;
    blackCanCastleKingside_ = other.blackCanCastleKingside_;
    blackCanCastleQueenside_ = other.blackCanCastleQueenside_;
}

void ChessBoard::clearBoard()
{
    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            board_[rank][file].reset();
        }
    }
    enPassantTarget_ = Position(-1, -1);
    whiteCanCastleKingside_ = true;
    whiteCanCastleQueenside_ = true;
    blackCanCastleKingside_ = true;
    blackCanCastleQueenside_ = true;
}

void ChessBoard::setupInitialPosition()
{
    clearBoard();

    // Set up white pieces (rank 0 and 1)
    board_[0][0] = std::make_unique<Rook>(Color::WHITE);
    board_[0][1] = std::make_unique<Knight>(Color::WHITE);
    board_[0][2] = std::make_unique<Bishop>(Color::WHITE);
    board_[0][3] = std::make_unique<Queen>(Color::WHITE);
    board_[0][4] = std::make_unique<King>(Color::WHITE);
    board_[0][5] = std::make_unique<Bishop>(Color::WHITE);
    board_[0][6] = std::make_unique<Knight>(Color::WHITE);
    board_[0][7] = std::make_unique<Rook>(Color::WHITE);

    for (int file = 0; file < 8; ++file)
    {
        board_[1][file] = std::make_unique<Pawn>(Color::WHITE);
    }

    // Set up black pieces (rank 6 and 7)
    for (int file = 0; file < 8; ++file)
    {
        board_[6][file] = std::make_unique<Pawn>(Color::BLACK);
    }

    board_[7][0] = std::make_unique<Rook>(Color::BLACK);
    board_[7][1] = std::make_unique<Knight>(Color::BLACK);
    board_[7][2] = std::make_unique<Bishop>(Color::BLACK);
    board_[7][3] = std::make_unique<Queen>(Color::BLACK);
    board_[7][4] = std::make_unique<King>(Color::BLACK);
    board_[7][5] = std::make_unique<Bishop>(Color::BLACK);
    board_[7][6] = std::make_unique<Knight>(Color::BLACK);
    board_[7][7] = std::make_unique<Rook>(Color::BLACK);
}

const Piece *ChessBoard::getPiece(const Position &pos) const
{
    if (!isValidPosition(pos))
        return nullptr;
    return board_[pos.rank][pos.file].get();
}

Piece *ChessBoard::getPiece(const Position &pos)
{
    if (!isValidPosition(pos))
        return nullptr;
    return board_[pos.rank][pos.file].get();
}

void ChessBoard::setPiece(const Position &pos, std::unique_ptr<Piece> piece)
{
    if (isValidPosition(pos))
    {
        board_[pos.rank][pos.file] = std::move(piece);
    }
}

void ChessBoard::removePiece(const Position &pos)
{
    if (isValidPosition(pos))
    {
        board_[pos.rank][pos.file].reset();
    }
}

bool ChessBoard::isValidPosition(const Position &pos) const
{
    return pos.isValid();
}

bool ChessBoard::isSquareEmpty(const Position &pos) const
{
    return getPiece(pos) == nullptr;
}

bool ChessBoard::isSquareOccupiedBy(const Position &pos, Color color) const
{
    const Piece *piece = getPiece(pos);
    return piece != nullptr && piece->getColor() == color;
}

bool ChessBoard::movePiece(const Position &from, const Position &to)
{
    if (!isValidPosition(from) || !isValidPosition(to))
        return false;

    Piece *piece = getPiece(from);
    if (!piece)
        return false;

    // Move the piece
    board_[to.rank][to.file] = std::move(board_[from.rank][from.file]);
    board_[from.rank][from.file].reset();

    // Mark piece as moved
    piece = getPiece(to);
    if (piece)
    {
        piece->setMoved(true);
    }

    return true;
}

Position ChessBoard::findKing(Color color) const
{
    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            const Piece *piece = getPiece(Position(rank, file));
            if (piece && piece->getType() == PieceType::KING && piece->getColor() == color)
            {
                return Position(rank, file);
            }
        }
    }
    return Position(-1, -1); // King not found (should never happen in valid game)
}

bool ChessBoard::isSquareAttackedBy(const Position &pos, Color attackingColor) const
{
    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            Position attackerPos(rank, file);
            const Piece *piece = getPiece(attackerPos);
            if (piece && piece->getColor() == attackingColor)
            {
                if (piece->canMoveTo(attackerPos, pos, *this))
                {
                    return true;
                }
            }
        }
    }
    return false;
}

bool ChessBoard::isInCheck(Color color) const
{
    Position kingPos = findKing(color);
    if (!kingPos.isValid())
        return false;
    return isSquareAttackedBy(kingPos, oppositeColor(color));
}

bool ChessBoard::isPathClear(const Position &from, const Position &to) const
{
    int rankDiff = to.rank - from.rank;
    int fileDiff = to.file - from.file;

    // Normalize direction
    int rankStep = (rankDiff == 0) ? 0 : (rankDiff > 0 ? 1 : -1);
    int fileStep = (fileDiff == 0) ? 0 : (fileDiff > 0 ? 1 : -1);

    Position current = from;
    current.rank += rankStep;
    current.file += fileStep;

    while (current != to)
    {
        if (!isSquareEmpty(current))
        {
            return false;
        }
        current.rank += rankStep;
        current.file += fileStep;
    }

    return true;
}

std::vector<Position> ChessBoard::getAllPiecesOfColor(Color color) const
{
    std::vector<Position> pieces;
    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            Position pos(rank, file);
            if (isSquareOccupiedBy(pos, color))
            {
                pieces.push_back(pos);
            }
        }
    }
    return pieces;
}

bool ChessBoard::canCastleKingside(Color color) const
{
    return (color == Color::WHITE) ? whiteCanCastleKingside_ : blackCanCastleKingside_;
}

bool ChessBoard::canCastleQueenside(Color color) const
{
    return (color == Color::WHITE) ? whiteCanCastleQueenside_ : blackCanCastleQueenside_;
}

void ChessBoard::setCastlingRights(Color color, bool kingside, bool queenside)
{
    if (color == Color::WHITE)
    {
        whiteCanCastleKingside_ = kingside;
        whiteCanCastleQueenside_ = queenside;
    }
    else
    {
        blackCanCastleKingside_ = kingside;
        blackCanCastleQueenside_ = queenside;
    }
}

std::vector<Position> ChessBoard::getAllValidMoves(Color color) const
{
    std::vector<Position> allMoves;
    std::vector<Position> pieces = getAllPiecesOfColor(color);

    for (const Position &piecePos : pieces)
    {
        const Piece *piece = getPiece(piecePos);
        if (piece)
        {
            std::vector<Position> pieceMoves = piece->getPossibleMoves(piecePos, *this);
            allMoves.insert(allMoves.end(), pieceMoves.begin(), pieceMoves.end());
        }
    }

    return allMoves;
}

bool ChessBoard::hasValidMoves(Color color) const
{
    std::vector<Position> pieces = getAllPiecesOfColor(color);

    for (const Position &piecePos : pieces)
    {
        const Piece *piece = getPiece(piecePos);
        if (piece)
        {
            std::vector<Position> moves = piece->getPossibleMoves(piecePos, *this);
            if (!moves.empty())
            {
                return true;
            }
        }
    }

    return false;
}

bool ChessBoard::isCheckmate(Color color) const
{
    return isInCheck(color) && !hasValidMoves(color);
}

bool ChessBoard::isStalemate(Color color) const
{
    return !isInCheck(color) && !hasValidMoves(color);
}

void ChessBoard::display() const
{
    std::cout << toString() << std::endl;
}

std::string ChessBoard::toString() const
{
    std::ostringstream oss;

    oss << "  +---+---+---+---+---+---+---+---+\n";
    for (int rank = 7; rank >= 0; --rank)
    {
        oss << (rank + 1) << " |";
        for (int file = 0; file < 8; ++file)
        {
            const Piece *piece = getPiece(Position(rank, file));
            char c = piece ? piece->getChar() : ' ';
            oss << " " << c << " |";
        }
        oss << "\n  +---+---+---+---+---+---+---+---+\n";
    }
    oss << "    a   b   c   d   e   f   g   h\n";

    return oss.str();
}

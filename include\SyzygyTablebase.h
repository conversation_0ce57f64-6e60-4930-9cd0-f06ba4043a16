#pragma once

#include "ChessTypes.h"
#include "ChessBoard.h"
#include "Move.h"
#include <string>
#include <vector>
#include <unordered_map>
#include <mutex>
#include <memory>
#include <atomic>

// Forward declaration for Fathom TB library
extern "C"
{
    struct TbRootMoves;
    struct TbPosition;
    struct TbCache;
}

// Syzygy Tablebase implementation using Fathom library
class SyzygyTablebase
{
public:
    // Result of a tablebase probe
    enum class ProbeResult
    {
        NONE,        // No result
        WIN,         // Win
        LOSS,        // Loss
        DRAW,        // Draw
        CURSED_WIN,  // Win but 50-move rule
        BLESSED_LOSS // Loss but 50-move rule
    };

    struct ProbeData
    {
        ProbeResult result;
        int score;     // Score in centipawns
        int dtz;       // Distance to zero (50-move rule counter reset)
        int dtm;       // Distance to mate (if available)
        Move bestMove; // Best move from this position

        ProbeData() : result(ProbeResult::NONE), score(0), dtz(0), dtm(0) {}
    };

    // Cache entry for tablebase results
    struct CacheEntry
    {
        uint64_t hash;  // Position hash
        ProbeData data; // Probe result data

        CacheEntry() : hash(0) {}
        CacheEntry(uint64_t h, const ProbeData &d) : hash(h), data(d) {}
    };

private:
    // Tablebase paths
    std::vector<std::string> tbPaths_;

    // Maximum number of pieces supported
    int maxPieces_;

    // Tablebase cache
    std::unordered_map<uint64_t, CacheEntry> tbCache_;
    std::mutex cacheMutex_;

    // Cache size
    size_t maxCacheSize_;

    // Fathom TB library state
    std::unique_ptr<TbCache, void (*)(TbCache *)> tbCache;

    // Statistics
    std::atomic<uint64_t> probeCount_;
    std::atomic<uint64_t> cacheHits_;
    std::atomic<uint64_t> probeFailed_;

    // Initialization state
    bool initialized_;

public:
    SyzygyTablebase();
    ~SyzygyTablebase();

    // Initialize the tablebase with paths
    bool initialize(const std::vector<std::string> &paths, int maxPieces = 7);

    // Check if tablebase is initialized
    bool isInitialized() const { return initialized_; }

    // Get maximum number of pieces supported
    int getMaxPieces() const { return maxPieces_; }

    // Set cache size (in MB)
    void setCacheSize(size_t sizeMB);

    // Clear the cache
    void clearCache();

    // Probe the tablebase for WDL (win/draw/loss)
    bool probeWDL(const ChessBoard &board, Color sideToMove, ProbeData &result);

    // Probe the tablebase for DTZ (distance to zero)
    bool probeDTZ(const ChessBoard &board, Color sideToMove, ProbeData &result);

    // Probe the tablebase for root moves
    bool probeRoot(const ChessBoard &board, Color sideToMove, std::vector<Move> &moves, ProbeData &result);

    // Check if position is a tablebase position
    bool isTablebasePosition(const ChessBoard &board) const;

    // Get statistics
    uint64_t getProbeCount() const { return probeCount_; }
    uint64_t getCacheHits() const { return cacheHits_; }
    uint64_t getProbeFailed() const { return probeFailed_; }
    size_t getCacheSize() const { return tbCache_.size(); }

private:
    // Convert ChessBoard to Fathom position
    bool boardToTbPosition(const ChessBoard &board, Color sideToMove, TbPosition &pos);

    // Convert Fathom move to engine Move
    Move tbMoveToMove(unsigned int tbMove, const ChessBoard &board);

    // Convert engine Move to Fathom move
    unsigned int moveToTbMove(const Move &move);

    // Add result to cache
    void cacheResult(uint64_t hash, const ProbeData &data);

    // Check cache for result
    bool checkCache(uint64_t hash, ProbeData &data);
};
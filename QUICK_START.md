# VibeChess Engine - Quick Start Guide

## Untuk Pengguna n Croissant GUI (Recommended)

### 1. Build Engine
```bash
# Windows
build.bat

# Linux/Mac
chmod +x build.sh
./build.sh

# Manual build
g++ -std=c++17 -Iinclude -O2 uci_main.cpp src/*.cpp -o chess_engine_uci
```

### 2. Setup di n Croissant
1. Buka n Croissant
2. Menu Engine → Add Engine
3. Browse ke `chess_engine_uci.exe` (Windows) atau `chess_engine_uci` (Linux/Mac)
4. Nama: "VibeChess"
5. Protocol: UCI
6. Klik OK

### 3. Konfigurasi Engine
**Parameter Penting:**
- **Hash**: 64-512 MB (sesuai RAM)
- **Threads**: 1-4 (sesuai CPU cores)
- **MultiPV**: 1 untuk permainan, 2-5 untuk analisis

**Cara Setting:**
- Di n Croissant: Engine Settings → Configure
- Command line: `./engine_config.exe --show`

## Untuk Console Play

### Build & Run
```bash
g++ -std=c++17 -Iinclude -O2 main.cpp src/*.cpp -o chess_engine
./chess_engine
```

### Commands
- `e2e4` - Gerakan (algebraic notation)
- `help` - Bantuan
- `quit` - Keluar

## Parameter Engine Utama

| Parameter | Range | Default | Deskripsi |
|-----------|-------|---------|-----------|
| Hash | 1-32768 MB | 32 | Ukuran hash table |
| Threads | 1-128 | 1 | Jumlah thread pencarian |
| MultiPV | 1-500 | 2 | Jumlah variasi terbaik |
| NUMA Offset | 0-7 | 0 | Optimasi memori |
| Table Memory | 1-8192 MB | 64 | Cache tablebase |
| Book File | string | - | Path opening book |
| SyzygyPath | string | - | Path tablebase |
| Overhead ms | 0-5000 | 50 | Overhead komunikasi |

## Konfigurasi Optimal

### Gaming (4GB RAM, 4 cores)
```
Hash=128
Threads=2
MultiPV=1
Overhead ms=50
```

### Analysis (8GB RAM, 8+ cores)
```
Hash=512
Threads=4
MultiPV=3
Table Memory=256
```

### Tournament
```
Hash=256
Threads=1
MultiPV=1
Overhead ms=30
Time Usage=95
```

## Files yang Dihasilkan

- `chess_engine_uci.exe` - Untuk GUI (n Croissant, Arena, dll)
- `chess_engine.exe` - Console version
- `engine_config.exe` - Configuration tool

## Test Engine

```bash
# Test UCI communication
echo "uci" | ./chess_engine_uci.exe

# Test basic functionality
echo -e "uci\nisready\nposition startpos\ngo depth 3\nquit" | ./chess_engine_uci.exe

# Show configuration
./engine_config.exe --show

# Export UCI options
./engine_config.exe --uci
```

## Troubleshooting

### Engine tidak terdeteksi di GUI
- Pastikan file executable dapat dijalankan
- Check path file sudah benar
- Test dengan command line terlebih dahulu

### Performa lambat
- Kurangi Hash size jika RAM terbatas
- Kurangi Threads jika CPU overload
- Increase Overhead ms untuk koneksi tidak stabil

### Build error
- Pastikan g++ terinstall dan mendukung C++17
- Check semua file source ada di direktori src/
- Pastikan include/ directory ada

## Fitur Utama

✅ **UCI Protocol** - Kompatibel dengan semua GUI modern  
✅ **Configurable Parameters** - 18+ parameter yang dapat disesuaikan  
✅ **Multi-threading** - Pencarian paralel hingga 128 threads  
✅ **Hash Tables** - Transposition table hingga 32GB  
✅ **Opening Book** - Support berbagai format book  
✅ **Syzygy Tablebase** - Perfect endgame play  
✅ **Advanced Search** - Null move, LMR, futility pruning  
✅ **Time Management** - Kontrol waktu yang presisi  

## Support

- **Documentation**: `ENGINE_CONFIG_README.md`
- **n Croissant Setup**: `CROISSANT_SETUP.md`
- **UCI Test**: `echo "uci" | ./chess_engine_uci.exe`

Engine ini telah ditest dan berfungsi dengan baik di n Croissant GUI!

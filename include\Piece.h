#pragma once

#include "ChessTypes.h"
#include <vector>
#include <memory>

// Forward declaration
class ChessBoard;

// Abstract base class for all chess pieces
class Piece {
protected:
    Color color_;
    PieceType type_;
    bool hasMoved_;

public:
    Piece(Color color, PieceType type) 
        : color_(color), type_(type), hasMoved_(false) {}
    
    virtual ~Piece() = default;
    
    // Getters
    Color getColor() const { return color_; }
    PieceType getType() const { return type_; }
    bool hasMovedBefore() const { return hasMoved_; }
    
    // Setters
    void setMoved(bool moved) { hasMoved_ = moved; }
    
    // Pure virtual functions that must be implemented by derived classes
    virtual std::vector<Position> getPossibleMoves(const Position& from, const ChessBoard& board) const = 0;
    virtual bool canMoveTo(const Position& from, const Position& to, const ChessBoard& board) const = 0;
    virtual std::unique_ptr<Piece> clone() const = 0;
    
    // Virtual function for piece-specific move validation
    virtual bool isValidMove(const Position& from, const Position& to, const ChessBoard& board) const {
        return canMoveTo(from, to, board);
    }
    
    // Get piece value for evaluation
    int getValue() const {
        return PIECE_VALUES[static_cast<int>(type_)];
    }
    
    // Get character representation
    char getChar() const {
        return pieceToChar(type_, color_);
    }
};

// Factory function to create pieces
std::unique_ptr<Piece> createPiece(PieceType type, Color color);

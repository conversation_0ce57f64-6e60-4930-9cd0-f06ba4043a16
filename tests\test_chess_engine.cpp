#include "../include/ChessEngine.h"
#include "../include/ChessGame.h"
#include <iostream>
#include <cassert>
#include <chrono>

class ChessEngineTest {
private:
    int testsRun_;
    int testsPassed_;
    
public:
    ChessEngineTest() : testsRun_(0), testsPassed_(0) {}
    
    void runAllTests() {
        std::cout << "=== CHESS ENGINE TEST SUITE ===\n\n";
        
        testBasicEvaluation();
        testTranspositionTable();
        testMoveOrdering();
        testQuiescenceSearch();
        testOpeningBook();
        testPerformance();
        
        std::cout << "\n=== TEST RESULTS ===\n";
        std::cout << "Tests run: " << testsRun_ << "\n";
        std::cout << "Tests passed: " << testsPassed_ << "\n";
        std::cout << "Success rate: " << (double)testsPassed_ / testsRun_ * 100 << "%\n";
    }
    
private:
    void assert_test(bool condition, const std::string& testName) {
        testsRun_++;
        if (condition) {
            testsPassed_++;
            std::cout << "[PASS] " << testName << "\n";
        } else {
            std::cout << "[FAIL] " << testName << "\n";
        }
    }
    
    void testBasicEvaluation() {
        std::cout << "Testing basic evaluation...\n";
        
        ChessEngine engine(Color::WHITE, 4);
        ChessGame game;
        
        // Test starting position evaluation
        int startingEval = engine.evaluatePosition(game.getBoard(), Color::WHITE);
        assert_test(abs(startingEval) < 100, "Starting position should be roughly equal");
        
        // Test after e2-e4
        Move e4(Position(1, 4), Position(3, 4));
        game.makeMove(e4);
        int afterE4 = engine.evaluatePosition(game.getBoard(), Color::WHITE);
        assert_test(afterE4 > startingEval, "e2-e4 should improve White's position");
        
        std::cout << "\n";
    }
    
    void testTranspositionTable() {
        std::cout << "Testing transposition table...\n";
        
        ChessEngine engine(Color::WHITE, 4);
        ChessGame game;
        
        // Clear TT and get baseline
        engine.clearTT();
        auto start = std::chrono::high_resolution_clock::now();
        Move move1 = engine.getBestMoveWithTime(game, 1000);
        auto end = std::chrono::high_resolution_clock::now();
        auto time1 = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        int nodes1 = engine.getNodesSearched();
        
        // Reset and search again (should be faster with TT)
        ChessGame game2;
        start = std::chrono::high_resolution_clock::now();
        Move move2 = engine.getBestMoveWithTime(game2, 1000);
        end = std::chrono::high_resolution_clock::now();
        auto time2 = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        int ttHits = engine.getTTHits();
        
        assert_test(move1.toAlgebraic() == move2.toAlgebraic(), "Same position should give same move");
        assert_test(ttHits > 0, "Should have transposition table hits");
        
        std::cout << "First search: " << time1.count() << "ms, " << nodes1 << " nodes\n";
        std::cout << "Second search: " << time2.count() << "ms, " << ttHits << " TT hits\n";
        std::cout << "\n";
    }
    
    void testMoveOrdering() {
        std::cout << "Testing move ordering...\n";
        
        ChessEngine engine(Color::WHITE, 4);
        ChessGame game;
        
        // Set up a tactical position
        // This is simplified - in a real test we'd set up specific positions
        std::vector<Move> moves = game.getAllValidMoves();
        assert_test(!moves.empty(), "Should have valid moves in starting position");
        assert_test(moves.size() == 20, "Starting position should have 20 legal moves");
        
        std::cout << "\n";
    }
    
    void testQuiescenceSearch() {
        std::cout << "Testing quiescence search...\n";
        
        ChessEngine engine(Color::WHITE, 4);
        ChessGame game;
        
        // Test that quiescence search is working
        // This is a basic test - in practice we'd set up tactical positions
        Move bestMove = engine.getBestMoveWithTime(game, 1000);
        assert_test(bestMove.isValid(), "Should return a valid move");
        
        int nodes = engine.getNodesSearched();
        assert_test(nodes > 0, "Should search some nodes");
        
        std::cout << "Searched " << nodes << " nodes\n";
        std::cout << "\n";
    }
    
    void testOpeningBook() {
        std::cout << "Testing opening book...\n";
        
        ChessEngine engine(Color::BLACK, 4);
        ChessGame game;
        
        // Test opening book integration
        bool hasOpening = engine.hasOpeningMove(game);
        assert_test(hasOpening, "Should have opening moves for starting position");
        
        if (hasOpening) {
            Move openingMove = engine.getOpeningMove(game);
            assert_test(openingMove.isValid(), "Opening move should be valid");
            std::cout << "Opening book suggests: " << openingMove.toAlgebraic() << "\n";
        }
        
        std::cout << "\n";
    }
    
    void testPerformance() {
        std::cout << "Testing performance...\n";
        
        ChessEngine engine(Color::WHITE, 5);
        ChessGame game;
        
        auto start = std::chrono::high_resolution_clock::now();
        Move bestMove = engine.getBestMoveWithTime(game, 2000); // 2 second search
        auto end = std::chrono::high_resolution_clock::now();
        
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
        int nodes = engine.getNodesSearched();
        int ttHits = engine.getTTHits();
        
        assert_test(bestMove.isValid(), "Should find a valid move");
        assert_test(duration.count() <= 2500, "Should respect time limit (with some tolerance)");
        assert_test(nodes > 1000, "Should search reasonable number of nodes");
        
        double nps = (double)nodes / duration.count() * 1000; // nodes per second
        std::cout << "Performance: " << nodes << " nodes in " << duration.count() << "ms\n";
        std::cout << "Speed: " << (int)nps << " nodes/second\n";
        std::cout << "TT hits: " << ttHits << " (" << (double)ttHits/nodes*100 << "%)\n";
        
        std::cout << "\n";
    }
};

int main() {
    ChessEngineTest tester;
    tester.runAllTests();
    return 0;
}

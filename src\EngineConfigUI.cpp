#include "EngineConfigUI.h"
#include <iostream>
#include <iomanip>
#include <sstream>
#include <algorithm>
#include <fstream>
#include <cctype>
#include <limits>

#ifdef _WIN32
#include <conio.h>
#include <windows.h>
#else
#include <termios.h>
#include <unistd.h>
#endif

EngineConfigUI::EngineConfigUI(EngineConfig &config) : config_(config), isRunning_(false) {}

void EngineConfigUI::run()
{
    isRunning_ = true;

    while (isRunning_)
    {
        clearScreen();
        displayHeader();
        displayMainMenu();

        std::string choice = getInput("Pilih opsi (1-9, q untuk keluar): ");

        if (!processMenuChoice(choice))
        {
            break;
        }
    }

    std::cout << "\nTerima kasih telah menggunakan konfigurasi engine!\n";
}

void EngineConfigUI::displayMainMenu()
{
    printSeparator('=', 60);
    std::cout << centerText("MENU KONFIGURASI ENGINE", 60) << "\n";
    printSeparator('=', 60);

    std::cout << "\n";
    std::cout << "1. Lihat Semua Parameter\n";
    std::cout << "2. Edit Parameter\n";
    std::cout << "3. Reset Parameter\n";
    std::cout << "4. Reset Semua ke Default\n";
    std::cout << "5. Muat Konfigurasi dari File\n";
    std::cout << "6. Simpan Konfigurasi ke File\n";
    std::cout << "7. Export ke Format UCI\n";
    std::cout << "8. Setup Cepat\n";
    std::cout << "9. Bantuan\n";
    std::cout << "q. Keluar\n";
    std::cout << "\n";
    printSeparator('-', 60);
}

bool EngineConfigUI::processMenuChoice(const std::string &choice)
{
    std::string lowerChoice = toLowerCase(choice);

    if (lowerChoice == "q" || lowerChoice == "quit" || lowerChoice == "keluar")
    {
        return false;
    }

    try
    {
        int menuChoice = std::stoi(choice);

        switch (menuChoice)
        {
        case 1:
            handleViewParameters();
            break;
        case 2:
            handleEditParameter();
            break;
        case 3:
            handleResetParameter();
            break;
        case 4:
            handleResetAll();
            break;
        case 5:
            handleLoadConfig();
            break;
        case 6:
            handleSaveConfig();
            break;
        case 7:
            handleExportUCI();
            break;
        case 8:
            quickSetup();
            break;
        case 9:
            displayHelp();
            break;
        default:
            std::cout << "Pilihan tidak valid. Tekan Enter untuk melanjutkan...";
            waitForEnter();
            break;
        }
    }
    catch (const std::exception &)
    {
        std::cout << "Input tidak valid. Tekan Enter untuk melanjutkan...";
        waitForEnter();
    }

    return true;
}

void EngineConfigUI::handleViewParameters()
{
    clearScreen();
    displayHeader();
    std::cout << centerText("DAFTAR PARAMETER ENGINE", 80) << "\n";
    printSeparator('=', 80);

    displayParameterList();

    std::cout << "\nTekan Enter untuk kembali ke menu utama...";
    waitForEnter();
}

void EngineConfigUI::displayParameterList() const
{
    std::vector<std::string> paramNames = config_.getAllParameterNames();

    // Sort parameter names for better display
    std::sort(paramNames.begin(), paramNames.end());

    std::cout << std::left;
    std::cout << std::setw(25) << "Parameter"
              << std::setw(15) << "Nilai Saat Ini"
              << std::setw(40) << "Deskripsi" << "\n";
    printSeparator('-', 80);

    for (const auto &name : paramNames)
    {
        const ConfigParameter *param = config_.getParameterInfo(name);
        if (param)
        {
            std::string value = formatParameterValue(param);
            std::string desc = param->description;

            // Truncate long descriptions
            if (desc.length() > 35)
            {
                desc = desc.substr(0, 32) + "...";
            }

            std::cout << std::setw(25) << name
                      << std::setw(15) << value
                      << std::setw(40) << desc << "\n";
        }
    }
}

void EngineConfigUI::handleEditParameter()
{
    clearScreen();
    displayHeader();
    std::cout << centerText("EDIT PARAMETER", 60) << "\n";
    printSeparator('=', 60);

    // Show available parameters
    std::cout << "\nParameter yang tersedia:\n";
    displayParameterList();

    std::string paramName = getInput("\nMasukkan nama parameter yang ingin diedit: ");

    if (paramName.empty())
    {
        return;
    }

    if (!config_.hasParameter(paramName))
    {
        std::cout << "Parameter '" << paramName << "' tidak ditemukan!\n";
        std::cout << "Tekan Enter untuk melanjutkan...";
        waitForEnter();
        return;
    }

    if (editParameter(paramName))
    {
        std::cout << "Parameter berhasil diubah!\n";
    }
    else
    {
        std::cout << "Gagal mengubah parameter.\n";
    }

    std::cout << "Tekan Enter untuk melanjutkan...";
    waitForEnter();
}

bool EngineConfigUI::editParameter(const std::string &paramName)
{
    const ConfigParameter *param = config_.getParameterInfo(paramName);
    if (!param)
    {
        return false;
    }

    std::cout << "\n"
              << centerText("EDIT: " + paramName, 60) << "\n";
    printSeparator('-', 60);
    std::cout << "Deskripsi: " << param->description << "\n";
    std::cout << "Nilai saat ini: " << param->currentValue << "\n";
    std::cout << "Nilai default: " << param->defaultValue << "\n";

    switch (param->type)
    {
    case ConfigParameter::SPIN:
        return editSpinParameter(param);
    case ConfigParameter::CHECK:
        return editCheckParameter(param);
    case ConfigParameter::STRING:
        return editStringParameter(param);
    case ConfigParameter::COMBO:
        return editComboParameter(param);
    case ConfigParameter::BUTTON:
        std::cout << "Parameter ini adalah tombol aksi.\n";
        return false;
    }

    return false;
}

bool EngineConfigUI::editSpinParameter(const ConfigParameter *param)
{
    std::cout << "Range: " << param->minValue << " - " << param->maxValue << "\n";

    int newValue = getIntInput("Masukkan nilai baru: ", param->minValue, param->maxValue);

    return config_.setParameter(param->name, std::to_string(newValue));
}

bool EngineConfigUI::editCheckParameter(const ConfigParameter *param)
{
    std::cout << "Nilai boolean (true/false)\n";

    bool newValue = getBoolInput("Masukkan nilai baru (y/n): ");

    return config_.setParameter(param->name, newValue ? "true" : "false");
}

bool EngineConfigUI::editStringParameter(const ConfigParameter *param)
{
    std::string newValue = getStringInput("Masukkan nilai baru: ");

    // Special handling for file paths
    if (param->name.find("File") != std::string::npos ||
        param->name.find("Path") != std::string::npos)
    {
        if (!newValue.empty() && !validateFilePath(newValue, false))
        {
            std::cout << "Peringatan: Path mungkin tidak valid.\n";
        }
    }

    return config_.setParameter(param->name, newValue);
}

bool EngineConfigUI::editComboParameter(const ConfigParameter *param)
{
    std::cout << "Pilihan yang tersedia:\n";
    for (size_t i = 0; i < param->options.size(); ++i)
    {
        std::cout << (i + 1) << ". " << param->options[i] << "\n";
    }

    int choice = getIntInput("Pilih opsi (nomor): ", 1, static_cast<int>(param->options.size()));

    return config_.setParameter(param->name, param->options[choice - 1]);
}

void EngineConfigUI::clearScreen() const
{
#ifdef _WIN32
    system("cls");
#else
    system("clear");
#endif
}

void EngineConfigUI::displayHeader() const
{
    printSeparator('*', 80);
    std::cout << centerText("VIBECHESS ENGINE CONFIGURATION", 80) << "\n";
    std::cout << centerText("Konfigurasi Parameter Engine Catur", 80) << "\n";
    printSeparator('*', 80);
    std::cout << "\n";
}

std::string EngineConfigUI::getInput(const std::string &prompt) const
{
    std::cout << prompt;
    std::string input;
    std::getline(std::cin, input);
    return trim(input);
}

int EngineConfigUI::getIntInput(const std::string &prompt, int min, int max) const
{
    while (true)
    {
        std::string input = getInput(prompt);

        try
        {
            int value = std::stoi(input);
            if (value >= min && value <= max)
            {
                return value;
            }
            else
            {
                std::cout << "Nilai harus antara " << min << " dan " << max << ". Coba lagi.\n";
            }
        }
        catch (const std::exception &)
        {
            std::cout << "Input tidak valid. Masukkan angka. Coba lagi.\n";
        }
    }
}

bool EngineConfigUI::getBoolInput(const std::string &prompt) const
{
    while (true)
    {
        std::string input = toLowerCase(getInput(prompt));

        if (input == "y" || input == "yes" || input == "true" || input == "1")
        {
            return true;
        }
        else if (input == "n" || input == "no" || input == "false" || input == "0")
        {
            return false;
        }
        else
        {
            std::cout << "Masukkan y/n, yes/no, true/false, atau 1/0. Coba lagi.\n";
        }
    }
}

std::string EngineConfigUI::getStringInput(const std::string &prompt) const
{
    return getInput(prompt);
}

std::string EngineConfigUI::getFileInput(const std::string &prompt, bool mustExist) const
{
    while (true)
    {
        std::string path = getInput(prompt);

        if (path.empty())
        {
            return path; // Allow empty paths
        }

        if (mustExist)
        {
            std::ifstream file(path);
            if (!file.good())
            {
                std::cout << "File tidak ditemukan: " << path << "\n";
                std::cout << "Coba lagi atau tekan Enter untuk kosong: ";
                continue;
            }
        }

        return path;
    }
}

void EngineConfigUI::handleResetParameter()
{
    clearScreen();
    displayHeader();
    std::cout << centerText("RESET PARAMETER", 60) << "\n";
    printSeparator('=', 60);

    displayParameterList();

    std::string paramName = getInput("\nMasukkan nama parameter yang ingin direset: ");

    if (paramName.empty())
    {
        return;
    }

    if (!config_.hasParameter(paramName))
    {
        std::cout << "Parameter '" << paramName << "' tidak ditemukan!\n";
    }
    else
    {
        config_.resetParameter(paramName);
        std::cout << "Parameter '" << paramName << "' berhasil direset ke nilai default.\n";
    }

    std::cout << "Tekan Enter untuk melanjutkan...";
    waitForEnter();
}

void EngineConfigUI::handleResetAll()
{
    clearScreen();
    displayHeader();
    std::cout << centerText("RESET SEMUA PARAMETER", 60) << "\n";
    printSeparator('=', 60);

    std::cout << "\nPeringatan: Ini akan mereset SEMUA parameter ke nilai default!\n";

    if (getBoolInput("Apakah Anda yakin? (y/n): "))
    {
        config_.resetToDefaults();
        std::cout << "Semua parameter berhasil direset ke nilai default.\n";
    }
    else
    {
        std::cout << "Reset dibatalkan.\n";
    }

    std::cout << "Tekan Enter untuk melanjutkan...";
    waitForEnter();
}

void EngineConfigUI::handleLoadConfig()
{
    clearScreen();
    displayHeader();
    std::cout << centerText("MUAT KONFIGURASI", 60) << "\n";
    printSeparator('=', 60);

    std::string filename = getFileInput("Masukkan nama file konfigurasi: ", true);

    if (filename.empty())
    {
        std::cout << "Operasi dibatalkan.\n";
    }
    else
    {
        if (config_.loadFromFile(filename))
        {
            std::cout << "Konfigurasi berhasil dimuat dari: " << filename << "\n";
        }
        else
        {
            std::cout << "Gagal memuat konfigurasi dari: " << filename << "\n";
        }
    }

    std::cout << "Tekan Enter untuk melanjutkan...";
    waitForEnter();
}

void EngineConfigUI::handleSaveConfig()
{
    clearScreen();
    displayHeader();
    std::cout << centerText("SIMPAN KONFIGURASI", 60) << "\n";
    printSeparator('=', 60);

    std::string filename = getInput("Masukkan nama file untuk menyimpan (default: engine_config.ini): ");

    if (filename.empty())
    {
        filename = "engine_config.ini";
    }

    if (config_.saveToFile(filename))
    {
        std::cout << "Konfigurasi berhasil disimpan ke: " << filename << "\n";
    }
    else
    {
        std::cout << "Gagal menyimpan konfigurasi ke: " << filename << "\n";
    }

    std::cout << "Tekan Enter untuk melanjutkan...";
    waitForEnter();
}

void EngineConfigUI::handleExportUCI()
{
    clearScreen();
    displayHeader();
    std::cout << centerText("EXPORT UCI OPTIONS", 60) << "\n";
    printSeparator('=', 60);

    std::string uciOptions = config_.getUCIOptionsString();

    std::cout << "\nUCI Options:\n";
    printSeparator('-', 60);
    std::cout << uciOptions << "\n";

    std::string filename = getInput("Simpan ke file (Enter untuk skip): ");

    if (!filename.empty())
    {
        std::ofstream file(filename);
        if (file.is_open())
        {
            file << uciOptions;
            std::cout << "UCI options berhasil disimpan ke: " << filename << "\n";
        }
        else
        {
            std::cout << "Gagal menyimpan ke file: " << filename << "\n";
        }
    }

    std::cout << "Tekan Enter untuk melanjutkan...";
    waitForEnter();
}

void EngineConfigUI::quickSetup()
{
    clearScreen();
    displayHeader();
    std::cout << centerText("SETUP CEPAT", 60) << "\n";
    printSeparator('=', 60);

    std::cout << "\nSetup cepat akan mengatur parameter utama engine.\n\n";

    // Hash table size
    int hashSize = getIntInput("Ukuran Hash Table (MB) [1-32768, default: 32]: ", 1, 32768);
    config_.setParameter("Hash", std::to_string(hashSize));

    // Number of threads
    int threads = getIntInput("Jumlah Thread [1-128, default: 1]: ", 1, 128);
    config_.setParameter("Threads", std::to_string(threads));

    // MultiPV
    int multiPV = getIntInput("Jumlah Principal Variations [1-500, default: 2]: ", 1, 500);
    config_.setParameter("MultiPV", std::to_string(multiPV));

    // Opening book
    bool useBook = getBoolInput("Gunakan Opening Book? (y/n): ");
    config_.setParameter("OwnBook", useBook ? "true" : "false");

    if (useBook)
    {
        std::string bookFile = getFileInput("Path ke Opening Book (kosong untuk default): ", false);
        if (!bookFile.empty())
        {
            config_.setParameter("Book File", bookFile);
        }
    }

    // Tablebase
    std::string tablebasePath = getFileInput("Path ke Syzygy Tablebase (kosong untuk skip): ", false);
    if (!tablebasePath.empty())
    {
        config_.setParameter("SyzygyPath", tablebasePath);

        int probeDepth = getIntInput("Syzygy Probe Depth [1-100, default: 1]: ", 1, 100);
        config_.setParameter("Syzygy Probe Depth", std::to_string(probeDepth));
    }

    std::cout << "\nSetup cepat selesai!\n";
    std::cout << "Tekan Enter untuk melanjutkan...";
    waitForEnter();
}

void EngineConfigUI::displayHelp() const
{
    clearScreen();
    displayHeader();
    std::cout << centerText("BANTUAN", 60) << "\n";
    printSeparator('=', 60);

    std::cout << "\nPARAMETER ENGINE:\n";
    printSeparator('-', 40);

    std::cout << "• NUMA Offset: Offset node NUMA untuk alokasi memori\n";
    std::cout << "• Threads: Jumlah thread pencarian paralel\n";
    std::cout << "• Hash: Ukuran hash table dalam MB\n";
    std::cout << "• Table Memory: Ukuran cache tablebase dalam MB\n";
    std::cout << "• MultiPV: Jumlah variasi utama yang ditampilkan\n";
    std::cout << "• Book File: Path ke file opening book\n";
    std::cout << "• Book Moves: Maksimal gerakan dari opening book\n";
    std::cout << "• Log File: Path file log engine\n";
    std::cout << "• Overhead ms: Overhead waktu dalam milidetik\n";
    std::cout << "• SyzygyPath: Path ke file Syzygy tablebase\n";
    std::cout << "• Syzygy Probe Depth: Kedalaman minimum probe tablebase\n";

    std::cout << "\nTIPS PENGGUNAAN:\n";
    printSeparator('-', 40);
    std::cout << "• Hash yang lebih besar = performa lebih baik (gunakan RAM yang tersedia)\n";
    std::cout << "• Thread lebih banyak = pencarian lebih cepat (sesuai CPU cores)\n";
    std::cout << "• MultiPV > 1 untuk analisis mendalam\n";
    std::cout << "• Opening book meningkatkan kualitas pembukaan\n";
    std::cout << "• Syzygy tablebase memberikan permainan endgame sempurna\n";

    std::cout << "\nTekan Enter untuk kembali...";
    waitForEnter();
}

bool EngineConfigUI::validateFilePath(const std::string &path, bool mustExist) const
{
    if (path.empty())
    {
        return true; // Empty paths are generally allowed
    }

    if (mustExist)
    {
        std::ifstream file(path);
        return file.good();
    }

    // Basic path validation - check for invalid characters
    const std::string invalidChars = "<>:\"|?*";
    for (char c : path)
    {
        if (invalidChars.find(c) != std::string::npos)
        {
            return false;
        }
    }

    return true;
}

std::string EngineConfigUI::formatParameterValue(const ConfigParameter *param) const
{
    if (!param)
    {
        return "";
    }

    std::string value = param->currentValue;

    // Format based on type
    switch (param->type)
    {
    case ConfigParameter::CHECK:
        return value == "true" ? "Ya" : "Tidak";
    case ConfigParameter::STRING:
        return value.empty() ? "(kosong)" : value;
    default:
        return value;
    }
}

std::string EngineConfigUI::centerText(const std::string &text, int width) const
{
    if (text.length() >= static_cast<size_t>(width))
    {
        return text;
    }

    int padding = (width - static_cast<int>(text.length())) / 2;
    return std::string(padding, ' ') + text;
}

std::string EngineConfigUI::padRight(const std::string &text, int width) const
{
    if (text.length() >= static_cast<size_t>(width))
    {
        return text.substr(0, width);
    }

    return text + std::string(width - text.length(), ' ');
}

void EngineConfigUI::printSeparator(char ch, int width) const
{
    std::cout << std::string(width, ch) << "\n";
}

void EngineConfigUI::waitForEnter()
{
    std::cin.ignore(std::numeric_limits<std::streamsize>::max(), '\n');
}

std::string EngineConfigUI::toLowerCase(const std::string &str)
{
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::tolower);
    return result;
}

std::string EngineConfigUI::trim(const std::string &str)
{
    size_t start = str.find_first_not_of(" \t\r\n");
    if (start == std::string::npos)
    {
        return "";
    }

    size_t end = str.find_last_not_of(" \t\r\n");
    return str.substr(start, end - start + 1);
}

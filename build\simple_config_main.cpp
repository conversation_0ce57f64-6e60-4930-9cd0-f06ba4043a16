#include "EngineConfig.h" 
#include <iostream> 
 
int main(int argc, char* argv[]) { 
    EngineConfig config; 
    if (argc > 1) { 
        std::string arg = argv[1]; 
        if (arg == "--show") { 
            std::cout << config.getAllParametersString(); 
        } else if (arg == "--uci") { 
            std::cout << config.getUCIOptionsString(); 
        } else if (arg == "--save" && argc > 2) { 
            if (config.saveToFile(argv[2])) { 
                std::cout << "Configuration saved to " << argv[2] << std::endl; 
            } else { 
                std::cout << "Failed to save configuration" << std::endl; 
            } 
        } else if (arg == "--load" && argc > 2) { 
            if (config.loadFromFile(argv[2])) { 
                std::cout << "Configuration loaded from " << argv[2] << std::endl; 
                std::cout << config.getAllParametersString(); 
            } else { 
                std::cout << "Failed to load configuration" << std::endl; 
            } 
        } else { 
            std::cout << "Usage: " << argv[0] << " [--show|--uci|--save file|--load file]" << std::endl; 
        } 
    } else { 
        std::cout << "VibeChess Engine Configuration" << std::endl; 
        std::cout << "Usage: " << argv[0] << " [--show|--uci|--save file|--load file]" << std::endl; 
    } 
    return 0; 
} 

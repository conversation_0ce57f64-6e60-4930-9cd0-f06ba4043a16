#pragma once

#include "ChessTypes.h"
#include "ChessBoard.h"
#include <unordered_map>
#include <string>
#include <vector>
#include <cstdint>

// Endgame Tablebase for perfect endgame play
class EndgameTablebase
{
public:
    enum class Result
    {
        WIN,
        DRAW,
        LOSS,
        UNKNOWN
    };

    struct TablebaseEntry
    {
        Result result;
        int distanceToMate; // Distance to mate (0 = immediate mate, -1 = draw)

        TablebaseEntry() : result(Result::UNKNOWN), distanceToMate(-1) {}
        TablebaseEntry(Result r, int dtm) : result(r), distanceToMate(dtm) {}
    };

private:
    // Hash table for storing tablebase positions
    std::unordered_map<uint64_t, TablebaseEntry> tablebaseCache_;

    // Supported endgames (basic implementation)
    bool isKnownEndgame(const ChessBoard &board) const;
    std::string getEndgameSignature(const ChessBoard &board) const;

    // Basic endgame evaluations
    TablebaseEntry evaluateKPvK(const ChessBoard &board, Color strongSide) const;
    TablebaseEntry evaluateKQvK(const ChessBoard &board, Color strongSide) const;
    TablebaseEntry evaluateKRvK(const ChessBoard &board, Color strongSide) const;
    TablebaseEntry evaluateKBBvK(const ChessBoard &board, Color strongSide) const;
    TablebaseEntry evaluateKBNvK(const ChessBoard &board, Color strongSide) const;

    // Helper functions
    Position findKing(const ChessBoard &board, Color color) const;
    std::vector<Position> findPieces(const ChessBoard &board, PieceType type, Color color) const;
    int manhattanDistance(const Position &a, const Position &b) const;
    int chebyshevDistance(const Position &a, const Position &b) const;
    bool isCornerMate(const Position &king, const Position &queen) const;

public:
    EndgameTablebase();
    ~EndgameTablebase() = default;

    // Main interface
    bool probe(const ChessBoard &board, Color sideToMove, TablebaseEntry &entry);
    bool isTablebasePosition(const ChessBoard &board) const;

    // Statistics
    size_t getCacheSize() const { return tablebaseCache_.size(); }
    void clearCache() { tablebaseCache_.clear(); }

    // Convert result to centipawn score
    int resultToScore(const TablebaseEntry &entry, Color perspective) const;
};

#include "EngineConfig.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <algorithm>
#include <cctype>

EngineConfig::EngineConfig() : configFilePath_("engine_config.ini")
{
    initializeDefaultParameters();
}

void EngineConfig::initializeDefaultParameters()
{
    // NUMA Offset
    ConfigParameter numaOffset(ConfigParameter::SPIN, "NUMA Offset",
                               "NUMA node offset for memory allocation", "0");
    numaOffset.minValue = 0;
    numaOffset.maxValue = 7;
    addParameter(numaOffset);

    // Threads
    ConfigParameter threads(ConfigParameter::SPIN, "Threads",
                            "Number of search threads", "1");
    threads.minValue = 1;
    threads.maxValue = 128;
    addParameter(threads);

    // Hash Table Size
    ConfigParameter hash(ConfigParameter::SPIN, "Hash",
                         "Hash table size in MB", "32");
    hash.minValue = 1;
    hash.maxValue = 32768;
    addParameter(hash);

    // Table Memory
    ConfigParameter tableMemory(ConfigParameter::SPIN, "Table Memory",
                                "Tablebase cache size in MB", "64");
    tableMemory.minValue = 1;
    tableMemory.maxValue = 8192;
    addParameter(tableMemory);

    // MultiPV
    ConfigParameter multiPV(ConfigParameter::SPIN, "MultiPV",
                            "Number of principal variations to show", "2");
    multiPV.minValue = 1;
    multiPV.maxValue = 500;
    addParameter(multiPV);

    // Book File
    ConfigParameter bookFile(ConfigParameter::STRING, "Book File",
                             "Opening book file path", "");
    addParameter(bookFile);

    // Book Moves
    ConfigParameter bookMoves(ConfigParameter::SPIN, "Book Moves",
                              "Maximum moves to use from opening book", "1000");
    bookMoves.minValue = 0;
    bookMoves.maxValue = 10000;
    addParameter(bookMoves);

    // Hash File Name
    ConfigParameter hashFileName(ConfigParameter::STRING, "Hash File Name",
                                 "Hash file for persistent storage", "");
    addParameter(hashFileName);

    // Log File
    ConfigParameter logFile(ConfigParameter::STRING, "Log File",
                            "Engine log file path", "");
    addParameter(logFile);

    // Overhead ms
    ConfigParameter overhead(ConfigParameter::SPIN, "Overhead ms",
                             "Time overhead in milliseconds", "50");
    overhead.minValue = 0;
    overhead.maxValue = 5000;
    addParameter(overhead);

    // Minimal Reporting
    ConfigParameter minReporting(ConfigParameter::SPIN, "Minimal Reporting",
                                 "Minimum time between reports in ms", "0");
    minReporting.minValue = 0;
    minReporting.maxValue = 10000;
    addParameter(minReporting);

    // Time Usage
    ConfigParameter timeUsage(ConfigParameter::SPIN, "Time Usage",
                              "Time usage percentage", "0");
    timeUsage.minValue = 0;
    timeUsage.maxValue = 100;
    addParameter(timeUsage);

    // Syzygy Path
    ConfigParameter syzygyPath(ConfigParameter::STRING, "SyzygyPath",
                               "Path to Syzygy tablebase files", "");
    addParameter(syzygyPath);

    // Syzygy Probe Depth
    ConfigParameter syzygyProbeDepth(ConfigParameter::SPIN, "Syzygy Probe Depth",
                                     "Minimum depth to probe Syzygy tablebases", "1");
    syzygyProbeDepth.minValue = 1;
    syzygyProbeDepth.maxValue = 100;
    addParameter(syzygyProbeDepth);

    // Additional UCI options
    ConfigParameter ponder(ConfigParameter::CHECK, "Ponder",
                           "Think on opponent's time", "false");
    addParameter(ponder);

    ConfigParameter ownBook(ConfigParameter::CHECK, "OwnBook",
                            "Use built-in opening book", "true");
    addParameter(ownBook);

    ConfigParameter nullMove(ConfigParameter::CHECK, "NullMove",
                             "Enable null move pruning", "true");
    addParameter(nullMove);
}

bool EngineConfig::addParameter(const ConfigParameter &param)
{
    auto paramPtr = std::make_unique<ConfigParameter>(param);
    parameters_[param.name] = std::move(paramPtr);
    return true;
}

bool EngineConfig::setParameter(const std::string &name, const std::string &value)
{
    auto it = parameters_.find(name);
    if (it == parameters_.end())
    {
        return false;
    }

    ConfigParameter *param = it->second.get();

    // Validate value based on parameter type
    switch (param->type)
    {
    case ConfigParameter::SPIN:
        if (!validateSpinValue(value, param->minValue, param->maxValue))
        {
            return false;
        }
        break;
    case ConfigParameter::CHECK:
        if (!validateBoolValue(value))
        {
            return false;
        }
        break;
    case ConfigParameter::COMBO:
        if (!validateComboValue(value, param->options))
        {
            return false;
        }
        break;
    case ConfigParameter::STRING:
        // String values are generally always valid
        break;
    case ConfigParameter::BUTTON:
        // Buttons don't store values
        if (param->onChange)
        {
            param->onChange(value);
        }
        return true;
    }

    // Custom validator
    if (param->validator && !param->validator(value))
    {
        return false;
    }

    param->currentValue = value;

    // Call onChange callback
    if (param->onChange)
    {
        param->onChange(value);
    }

    return true;
}

std::string EngineConfig::getParameter(const std::string &name) const
{
    auto it = parameters_.find(name);
    if (it != parameters_.end())
    {
        return it->second->currentValue;
    }
    return "";
}

bool EngineConfig::hasParameter(const std::string &name) const
{
    return parameters_.find(name) != parameters_.end();
}

int EngineConfig::getIntParameter(const std::string &name) const
{
    std::string value = getParameter(name);
    if (!value.empty())
    {
        try
        {
            return std::stoi(value);
        }
        catch (const std::exception &)
        {
            // Return default if conversion fails
            auto it = parameters_.find(name);
            if (it != parameters_.end())
            {
                try
                {
                    return std::stoi(it->second->defaultValue);
                }
                catch (const std::exception &)
                {
                    return 0;
                }
            }
        }
    }
    return 0;
}

bool EngineConfig::getBoolParameter(const std::string &name) const
{
    std::string value = toLowerCase(getParameter(name));
    return value == "true" || value == "1" || value == "yes" || value == "on";
}

std::string EngineConfig::getStringParameter(const std::string &name) const
{
    return getParameter(name);
}

const ConfigParameter *EngineConfig::getParameterInfo(const std::string &name) const
{
    auto it = parameters_.find(name);
    if (it != parameters_.end())
    {
        return it->second.get();
    }
    return nullptr;
}

std::vector<std::string> EngineConfig::getAllParameterNames() const
{
    std::vector<std::string> names;
    for (const auto &pair : parameters_)
    {
        names.push_back(pair.first);
    }
    return names;
}

std::string EngineConfig::toLowerCase(const std::string &str) const
{
    std::string result = str;
    std::transform(result.begin(), result.end(), result.begin(), ::tolower);
    return result;
}

bool EngineConfig::validateSpinValue(const std::string &value, int min, int max) const
{
    try
    {
        int intValue = std::stoi(value);
        return intValue >= min && intValue <= max;
    }
    catch (const std::exception &)
    {
        return false;
    }
}

bool EngineConfig::validateBoolValue(const std::string &value) const
{
    std::string lowerValue = toLowerCase(value);
    return lowerValue == "true" || lowerValue == "false" ||
           lowerValue == "1" || lowerValue == "0" ||
           lowerValue == "yes" || lowerValue == "no" ||
           lowerValue == "on" || lowerValue == "off";
}

bool EngineConfig::validateComboValue(const std::string &value,
                                      const std::vector<std::string> &options) const
{
    return std::find(options.begin(), options.end(), value) != options.end();
}

bool EngineConfig::loadFromFile(const std::string &filename)
{
    std::ifstream file(filename);
    if (!file.is_open())
    {
        return false;
    }

    std::string line;
    while (std::getline(file, line))
    {
        // Skip empty lines and comments
        if (line.empty() || line[0] == '#' || line[0] == ';')
        {
            continue;
        }

        // Parse key=value pairs
        size_t equalPos = line.find('=');
        if (equalPos != std::string::npos)
        {
            std::string key = line.substr(0, equalPos);
            std::string value = line.substr(equalPos + 1);

            // Trim whitespace
            key.erase(0, key.find_first_not_of(" \t"));
            key.erase(key.find_last_not_of(" \t") + 1);
            value.erase(0, value.find_first_not_of(" \t"));
            value.erase(value.find_last_not_of(" \t") + 1);

            setParameter(key, value);
        }
    }

    configFilePath_ = filename;
    return true;
}

bool EngineConfig::saveToFile(const std::string &filename) const
{
    std::ofstream file(filename);
    if (!file.is_open())
    {
        return false;
    }

    file << "# VibeChess Engine Configuration\n";
    file << "# Generated automatically - modify with care\n\n";

    for (const auto &pair : parameters_)
    {
        const ConfigParameter *param = pair.second.get();
        file << "# " << param->description << "\n";
        file << param->name << "=" << param->currentValue << "\n\n";
    }

    return true;
}

void EngineConfig::resetToDefaults()
{
    for (auto &pair : parameters_)
    {
        pair.second->currentValue = pair.second->defaultValue;
    }
}

void EngineConfig::resetParameter(const std::string &name)
{
    auto it = parameters_.find(name);
    if (it != parameters_.end())
    {
        it->second->currentValue = it->second->defaultValue;
    }
}

std::string EngineConfig::getParameterDisplayString(const std::string &name) const
{
    auto it = parameters_.find(name);
    if (it == parameters_.end())
    {
        return "";
    }

    const ConfigParameter *param = it->second.get();
    std::ostringstream oss;

    oss << param->name << ": " << param->currentValue;

    switch (param->type)
    {
    case ConfigParameter::SPIN:
        oss << " (range: " << param->minValue << "-" << param->maxValue << ")";
        break;
    case ConfigParameter::CHECK:
        oss << " (boolean)";
        break;
    case ConfigParameter::COMBO:
        oss << " (options: ";
        for (size_t i = 0; i < param->options.size(); ++i)
        {
            if (i > 0)
                oss << ", ";
            oss << param->options[i];
        }
        oss << ")";
        break;
    case ConfigParameter::STRING:
        oss << " (string)";
        break;
    case ConfigParameter::BUTTON:
        oss << " (button)";
        break;
    }

    return oss.str();
}

std::string EngineConfig::getAllParametersString() const
{
    std::ostringstream oss;
    oss << "=== Engine Configuration ===\n\n";

    for (const auto &pair : parameters_)
    {
        oss << getParameterDisplayString(pair.first) << "\n";
        oss << "  Description: " << pair.second->description << "\n\n";
    }

    return oss.str();
}

std::string EngineConfig::getUCIOptionsString() const
{
    std::ostringstream oss;

    for (const auto &pair : parameters_)
    {
        const ConfigParameter *param = pair.second.get();

        oss << "option name " << param->name << " type ";

        switch (param->type)
        {
        case ConfigParameter::SPIN:
            oss << "spin default " << param->defaultValue
                << " min " << param->minValue
                << " max " << param->maxValue;
            break;
        case ConfigParameter::CHECK:
            oss << "check default " << param->defaultValue;
            break;
        case ConfigParameter::COMBO:
            oss << "combo default " << param->defaultValue;
            for (const auto &option : param->options)
            {
                oss << " var " << option;
            }
            break;
        case ConfigParameter::STRING:
            oss << "string default " << param->defaultValue;
            break;
        case ConfigParameter::BUTTON:
            oss << "button";
            break;
        }

        oss << "\n";
    }

    return oss.str();
}

bool EngineConfig::setUCIOption(const std::string &name, const std::string &value)
{
    return setParameter(name, value);
}

#pragma once

#include <vector>
#include <thread>
#include <mutex>
#include <condition_variable>
#include <queue>
#include <functional>
#include <atomic>
#include <future>

// Thread pool for parallel search in chess engine
class ThreadPool {
private:
    std::vector<std::thread> workers_;
    std::queue<std::function<void()>> tasks_;
    
    // Synchronization
    std::mutex queueMutex_;
    std::condition_variable condition_;
    std::atomic<bool> stop_;
    std::atomic<int> activeThreads_;
    
    // Thread local storage for search state
    struct ThreadData {
        int id;
        uint64_t nodes;
        uint64_t tbHits;
        // Add more thread-specific data as needed
    };
    
    std::vector<ThreadData> threadData_;
    
 public:
    ThreadPool(size_t threads);
    ~ThreadPool();
    
    // Disable copying
    ThreadPool(const ThreadPool&) = delete;
    ThreadPool& operator=(const ThreadPool&) = delete;
    
    // Add task to the thread pool
    template<class F, class... Args>
    auto enqueue(F&& f, Args&&... args) 
        -> std::future<typename std::result_of<F(Args...)>::type>;
    
    // Get the number of threads
    size_t size() const { return workers_.size(); }
    
    // Get the number of active threads
    int activeThreads() const { return activeThreads_; }
    
    // Get thread-specific data
    const ThreadData& getThreadData(size_t threadId) const {
        return threadData_[threadId];
    }
    
    // Reset thread-specific data
    void resetThreadData();
    
    // Wait for all tasks to complete
    void waitForTasks();
};

// Template implementation
template<class F, class... Args>
auto ThreadPool::enqueue(F&& f, Args&&... args) 
    -> std::future<typename std::result_of<F(Args...)>::type> {
    
    using return_type = typename std::result_of<F(Args...)>::type;
    
    auto task = std::make_shared<std::packaged_task<return_type()>>(
        std::bind(std::forward<F>(f), std::forward<Args>(args)...)
    );
    
    std::future<return_type> res = task->get_future();
    {
        std::unique_lock<std::mutex> lock(queueMutex_);
        
        // Don't allow enqueueing after stopping the pool
        if (stop_) {
            throw std::runtime_error("enqueue on stopped ThreadPool");
        }
        
        tasks_.emplace([task](){ (*task)(); });
    }
    condition_.notify_one();
    return res;
}
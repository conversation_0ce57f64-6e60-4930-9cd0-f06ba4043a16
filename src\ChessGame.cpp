#include "ChessGame.h"
#include <iostream>
#include <sstream>

ChessGame::ChessGame()
{
    startNewGame();
}

void ChessGame::startNewGame()
{
    board_.setupInitialPosition();
    currentPlayer_ = Color::WHITE;
    gameState_ = GameState::PLAYING;
    moveHistory_.clear();
    halfMoveClock_ = 0;
    fullMoveNumber_ = 1;
}

bool ChessGame::makeMove(const Move &move)
{
    if (!isValidMove(move))
    {
        return false;
    }

    // Check if move would leave king in check
    if (wouldBeInCheckAfterMove(move))
    {
        return false;
    }

    // Store move in history
    moveHistory_.push_back(move);

    // Get pieces for move tracking
    const Piece *movingPiece = board_.getPiece(move.getFrom());
    const Piece *capturedPiece = board_.getPiece(move.getTo());

    // Update half-move clock
    if (movingPiece && movingPiece->getType() == PieceType::PAWN || capturedPiece)
    {
        halfMoveClock_ = 0;
    }
    else
    {
        halfMoveClock_++;
    }

    // Handle special moves
    handleSpecialMoves(move);

    // Make the move
    board_.movePiece(move.getFrom(), move.getTo());

    // Handle pawn promotion
    if (move.getType() == MoveType::PAWN_PROMOTION)
    {
        board_.setPiece(move.getTo(), createPiece(move.getPromotionPiece(), currentPlayer_));
    }

    // Update castling rights if king or rook moved
    if (movingPiece)
    {
        if (movingPiece->getType() == PieceType::KING)
        {
            board_.setCastlingRights(currentPlayer_, false, false);
        }
        else if (movingPiece->getType() == PieceType::ROOK)
        {
            Position from = move.getFrom();
            if (from.file == 0)
            { // Queenside rook
                board_.setCastlingRights(currentPlayer_, board_.canCastleKingside(currentPlayer_), false);
            }
            else if (from.file == 7)
            { // Kingside rook
                board_.setCastlingRights(currentPlayer_, false, board_.canCastleQueenside(currentPlayer_));
            }
        }
    }

    // Switch players
    switchPlayer();

    // Update full move number
    if (currentPlayer_ == Color::WHITE)
    {
        fullMoveNumber_++;
    }

    // Update game state
    updateGameState();

    return true;
}

bool ChessGame::makeMove(const std::string &algebraicMove)
{
    Move move = Move::fromAlgebraic(algebraicMove);
    return makeMove(move);
}

bool ChessGame::isValidMove(const Move &move) const
{
    if (!move.isValid())
        return false;

    const Piece *piece = board_.getPiece(move.getFrom());
    if (!piece || piece->getColor() != currentPlayer_)
    {
        return false;
    }

    return piece->canMoveTo(move.getFrom(), move.getTo(), board_);
}

std::vector<Move> ChessGame::getAllValidMoves() const
{
    std::vector<Move> validMoves;

    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            Position pos(rank, file);
            const Piece *piece = board_.getPiece(pos);
            if (piece && piece->getColor() == currentPlayer_)
            {
                std::vector<Move> pieceMoves = getAllValidMovesForPiece(pos);
                validMoves.insert(validMoves.end(), pieceMoves.begin(), pieceMoves.end());
            }
        }
    }

    return validMoves;
}

std::vector<Move> ChessGame::getAllValidMovesForPiece(const Position &pos) const
{
    std::vector<Move> validMoves;
    const Piece *piece = board_.getPiece(pos);

    if (!piece || piece->getColor() != currentPlayer_)
    {
        return validMoves;
    }

    std::vector<Position> possibleMoves = piece->getPossibleMoves(pos, board_);

    for (const Position &to : possibleMoves)
    {
        Move move(pos, to);

        // Check for pawn promotion
        if (piece->getType() == PieceType::PAWN)
        {
            int promotionRank = (currentPlayer_ == Color::WHITE) ? 7 : 0;
            if (to.rank == promotionRank)
            {
                // Add all promotion options
                validMoves.push_back(Move(pos, to, PieceType::QUEEN));
                validMoves.push_back(Move(pos, to, PieceType::ROOK));
                validMoves.push_back(Move(pos, to, PieceType::BISHOP));
                validMoves.push_back(Move(pos, to, PieceType::KNIGHT));
                continue;
            }
        }

        // Check for castling
        if (piece->getType() == PieceType::KING && std::abs(to.file - pos.file) == 2)
        {
            if (to.file == 6)
            {
                move = Move(pos, to, MoveType::CASTLE_KINGSIDE);
            }
            else if (to.file == 2)
            {
                move = Move(pos, to, MoveType::CASTLE_QUEENSIDE);
            }
        }

        // Check for en passant
        if (piece->getType() == PieceType::PAWN && to == board_.getEnPassantTarget())
        {
            move = Move(pos, to, MoveType::EN_PASSANT);
        }

        if (!wouldBeInCheckAfterMove(move))
        {
            validMoves.push_back(move);
        }
    }

    return validMoves;
}

bool ChessGame::wouldBeInCheckAfterMove(const Move &move) const
{
    // Create a copy of the board and make the move
    ChessBoard tempBoard = board_;
    tempBoard.movePiece(move.getFrom(), move.getTo());

    // Check if current player's king would be in check
    return tempBoard.isInCheck(currentPlayer_);
}

void ChessGame::handleSpecialMoves(const Move &move)
{
    const Piece *piece = board_.getPiece(move.getFrom());
    if (!piece)
        return;

    // Handle en passant capture
    if (move.getType() == MoveType::EN_PASSANT)
    {
        int captureRank = (currentPlayer_ == Color::WHITE) ? move.getTo().rank - 1 : move.getTo().rank + 1;
        board_.removePiece(Position(captureRank, move.getTo().file));
    }

    // Handle castling
    else if (move.getType() == MoveType::CASTLE_KINGSIDE)
    {
        // Move the rook
        Position rookFrom(move.getFrom().rank, 7);
        Position rookTo(move.getFrom().rank, 5);
        board_.movePiece(rookFrom, rookTo);
    }
    else if (move.getType() == MoveType::CASTLE_QUEENSIDE)
    {
        // Move the rook
        Position rookFrom(move.getFrom().rank, 0);
        Position rookTo(move.getFrom().rank, 3);
        board_.movePiece(rookFrom, rookTo);
    }

    // Set en passant target for pawn double moves
    if (piece->getType() == PieceType::PAWN && std::abs(move.getTo().rank - move.getFrom().rank) == 2)
    {
        int enPassantRank = (move.getFrom().rank + move.getTo().rank) / 2;
        board_.setEnPassantTarget(Position(enPassantRank, move.getFrom().file));
    }
    else
    {
        board_.clearEnPassantTarget();
    }
}

void ChessGame::switchPlayer()
{
    currentPlayer_ = oppositeColor(currentPlayer_);
}

void ChessGame::updateGameState()
{
    if (board_.isInCheck(currentPlayer_))
    {
        if (board_.isCheckmate(currentPlayer_))
        {
            gameState_ = GameState::CHECKMATE;
        }
        else
        {
            gameState_ = GameState::CHECK;
        }
    }
    else if (board_.isStalemate(currentPlayer_))
    {
        gameState_ = GameState::STALEMATE;
    }
    else if (isDraw())
    {
        gameState_ = GameState::DRAW;
    }
    else
    {
        gameState_ = GameState::PLAYING;
    }
}

bool ChessGame::isInCheck() const
{
    return board_.isInCheck(currentPlayer_);
}

bool ChessGame::isCheckmate() const
{
    return board_.isCheckmate(currentPlayer_);
}

bool ChessGame::isStalemate() const
{
    return board_.isStalemate(currentPlayer_);
}

bool ChessGame::isDraw() const
{
    return isFiftyMoveRule() || isThreefoldRepetition();
}

bool ChessGame::isFiftyMoveRule() const
{
    return halfMoveClock_ >= 100; // 50 moves for each player
}

bool ChessGame::isThreefoldRepetition() const
{
    // Simplified implementation - in a full engine, you'd track board positions
    return false;
}

void ChessGame::displayBoard() const
{
    board_.display();
}

std::string ChessGame::getBoardString() const
{
    return board_.toString();
}

std::string ChessGame::getGameStatusString() const
{
    std::ostringstream oss;

    oss << "Current player: " << colorToString(currentPlayer_) << "\n";

    switch (gameState_)
    {
    case GameState::PLAYING:
        oss << "Game in progress";
        break;
    case GameState::CHECK:
        oss << colorToString(currentPlayer_) << " is in check!";
        break;
    case GameState::CHECKMATE:
        oss << "Checkmate! " << colorToString(oppositeColor(currentPlayer_)) << " wins!";
        break;
    case GameState::STALEMATE:
        oss << "Stalemate! Game is a draw.";
        break;
    case GameState::DRAW:
        oss << "Draw!";
        break;
    }

    return oss.str();
}

std::string ChessGame::getLastMoveString() const
{
    if (moveHistory_.empty())
    {
        return "No moves yet";
    }
    return moveHistory_.back().toAlgebraic();
}

#pragma once

#include "ChessTypes.h"
#include "ChessBoard.h"
#include "Move.h"
#include <vector>

class TacticalPatterns
{
public:
    // Tactical pattern bonuses
    static const int PIN_BONUS = 50;
    static const int FORK_BONUS = 75;
    static const int SKEWER_BONUS = 60;
    static const int DISCOVERED_ATTACK_BONUS = 80;
    static const int DOUBLE_ATTACK_BONUS = 40;
    static const int TRAPPED_PIECE_PENALTY = 100;
    static const int HANGING_PIECE_PENALTY = 150;

    // Detect if a piece is pinned
    static bool isPinned(const ChessBoard &board, const Position &pos, Color color)
    {
        const Piece *piece = board.getPiece(pos);
        if (!piece || piece->getColor() != color)
        {
            return false;
        }

        Position kingPos = board.findKing(color);
        if (!kingPos.isValid())
        {
            return false;
        }

        // Check if piece is on the same line as king
        if (!isOnSameLine(pos, kingPos))
        {
            return false;
        }

        // Check if there's an enemy piece that can attack through this piece to the king
        Color enemyColor = oppositeColor(color);

        // Get direction from king to piece
        int deltaRank = pos.rank - kingPos.rank;
        int deltaFile = pos.file - kingPos.file;

        // Normalize direction
        int dirRank = (deltaRank == 0) ? 0 : (deltaRank > 0 ? 1 : -1);
        int dirFile = (deltaFile == 0) ? 0 : (deltaFile > 0 ? 1 : -1);

        // Look beyond the piece for enemy attackers
        Position checkPos = pos;
        checkPos.rank += dirRank;
        checkPos.file += dirFile;

        while (checkPos.isValid())
        {
            const Piece *checkPiece = board.getPiece(checkPos);
            if (checkPiece)
            {
                if (checkPiece->getColor() == enemyColor)
                {
                    // Check if this piece can attack along this line
                    if (canAttackAlongLine(checkPiece, checkPos, kingPos))
                    {
                        return true;
                    }
                }
                break; // Found a piece, stop looking
            }
            checkPos.rank += dirRank;
            checkPos.file += dirFile;
        }

        return false;
    }

    // Detect fork (one piece attacking two or more enemy pieces)
    static int detectFork(const ChessBoard &board, const Position &pos, Color color)
    {
        const Piece *piece = board.getPiece(pos);
        if (!piece || piece->getColor() != color)
        {
            return 0;
        }

        std::vector<Position> attacks = piece->getPossibleMoves(pos, board);
        std::vector<Position> enemyPieces;

        Color enemyColor = oppositeColor(color);

        // Find enemy pieces that can be attacked
        for (const Position &attackPos : attacks)
        {
            const Piece *target = board.getPiece(attackPos);
            if (target && target->getColor() == enemyColor)
            {
                enemyPieces.push_back(attackPos);
            }
        }

        // Fork bonus based on number and value of pieces attacked
        if (enemyPieces.size() >= 2)
        {
            int bonus = FORK_BONUS;

            // Extra bonus for forking king
            for (const Position &enemyPos : enemyPieces)
            {
                const Piece *enemyPiece = board.getPiece(enemyPos);
                if (enemyPiece && enemyPiece->getType() == PieceType::KING)
                {
                    bonus += 50;
                    break;
                }
            }

            return bonus;
        }

        return 0;
    }

    // Detect skewer (attacking a valuable piece with a less valuable piece behind it)
    static int detectSkewer(const ChessBoard &board, const Position &pos, Color color)
    {
        const Piece *piece = board.getPiece(pos);
        if (!piece || piece->getColor() != color)
        {
            return 0;
        }

        std::vector<Position> attacks = piece->getPossibleMoves(pos, board);
        Color enemyColor = oppositeColor(color);

        for (const Position &attackPos : attacks)
        {
            const Piece *target = board.getPiece(attackPos);
            if (target && target->getColor() == enemyColor)
            {
                // Check if there's a less valuable piece behind the target
                int deltaRank = attackPos.rank - pos.rank;
                int deltaFile = attackPos.file - pos.file;

                // Normalize direction
                int dirRank = (deltaRank == 0) ? 0 : (deltaRank > 0 ? 1 : -1);
                int dirFile = (deltaFile == 0) ? 0 : (deltaFile > 0 ? 1 : -1);

                Position behindPos = attackPos;
                behindPos.rank += dirRank;
                behindPos.file += dirFile;

                if (behindPos.isValid())
                {
                    const Piece *behindPiece = board.getPiece(behindPos);
                    if (behindPiece && behindPiece->getColor() == enemyColor &&
                        behindPiece->getValue() < target->getValue())
                    {
                        return SKEWER_BONUS;
                    }
                }
            }
        }

        return 0;
    }

    // Detect discovered attack
    static int detectDiscoveredAttack(const ChessBoard &board, const Move &move, Color color)
    {
        // For now, return a simple bonus for moves that might create discovered attacks
        // This is a simplified implementation - full implementation would require
        // proper board simulation which is complex with the current ChessBoard interface

        const Piece *movingPiece = board.getPiece(move.getFrom());
        if (!movingPiece)
        {
            return 0;
        }

        // Simple heuristic: moving pieces away from back rank might create discovered attacks
        if (movingPiece->getType() != PieceType::PAWN &&
            movingPiece->getType() != PieceType::KING)
        {
            return DISCOVERED_ATTACK_BONUS / 4; // Reduced bonus for simplified detection
        }

        return 0;
    }

    // Detect hanging pieces (undefended pieces)
    static int detectHangingPieces(const ChessBoard &board, Color color)
    {
        int penalty = 0;
        Color enemyColor = oppositeColor(color);

        for (int rank = 0; rank < 8; ++rank)
        {
            for (int file = 0; file < 8; ++file)
            {
                Position pos(rank, file);
                const Piece *piece = board.getPiece(pos);

                if (piece && piece->getColor() == color)
                {
                    // Check if piece is attacked by enemy
                    if (isAttackedBy(board, pos, enemyColor))
                    {
                        // Check if piece is defended
                        if (!isDefendedBy(board, pos, color))
                        {
                            penalty += HANGING_PIECE_PENALTY;
                        }
                    }
                }
            }
        }

        return penalty;
    }

    // Evaluate all tactical patterns for a position
    static int evaluateTacticalPatterns(const ChessBoard &board, Color color)
    {
        int score = 0;

        for (int rank = 0; rank < 8; ++rank)
        {
            for (int file = 0; file < 8; ++file)
            {
                Position pos(rank, file);
                const Piece *piece = board.getPiece(pos);

                if (piece && piece->getColor() == color)
                {
                    score += detectFork(board, pos, color);
                    score += detectSkewer(board, pos, color);

                    // Penalty for pinned pieces
                    if (isPinned(board, pos, color))
                    {
                        score -= PIN_BONUS;
                    }
                }
            }
        }

        // Subtract penalty for hanging pieces
        score -= detectHangingPieces(board, color);

        return score;
    }

private:
    static bool isOnSameLine(const Position &pos1, const Position &pos2)
    {
        return pos1.rank == pos2.rank || pos1.file == pos2.file ||
               abs(pos1.rank - pos2.rank) == abs(pos1.file - pos2.file);
    }

    static bool canAttackAlongLine(const Piece *piece, const Position &from, const Position &to)
    {
        PieceType type = piece->getType();

        int deltaRank = abs(to.rank - from.rank);
        int deltaFile = abs(to.file - from.file);

        switch (type)
        {
        case PieceType::ROOK:
            return deltaRank == 0 || deltaFile == 0;
        case PieceType::BISHOP:
            return deltaRank == deltaFile;
        case PieceType::QUEEN:
            return deltaRank == 0 || deltaFile == 0 || deltaRank == deltaFile;
        default:
            return false;
        }
    }

    static bool isPositionBetween(const Position &pos, const Position &start, const Position &end)
    {
        if (!isOnSameLine(start, end))
        {
            return false;
        }

        int minRank = std::min(start.rank, end.rank);
        int maxRank = std::max(start.rank, end.rank);
        int minFile = std::min(start.file, end.file);
        int maxFile = std::max(start.file, end.file);

        return pos.rank >= minRank && pos.rank <= maxRank &&
               pos.file >= minFile && pos.file <= maxFile &&
               pos != start && pos != end;
    }

    static bool isAttackedBy(const ChessBoard &board, const Position &pos, Color attackerColor)
    {
        // Simplified attack detection
        for (int rank = 0; rank < 8; ++rank)
        {
            for (int file = 0; file < 8; ++file)
            {
                Position attackerPos(rank, file);
                const Piece *attacker = board.getPiece(attackerPos);

                if (attacker && attacker->getColor() == attackerColor)
                {
                    std::vector<Position> attacks = attacker->getPossibleMoves(attackerPos, board);
                    for (const Position &attackPos : attacks)
                    {
                        if (attackPos == pos)
                        {
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    }

    static bool isDefendedBy(const ChessBoard &board, const Position &pos, Color defenderColor)
    {
        // Similar to isAttackedBy but for defenders
        return isAttackedBy(board, pos, defenderColor);
    }
};

# VibeChess Engine Configuration System

Sistem konfigurasi engine yang komprehensif untuk VibeChess, memungkinkan pengaturan parameter engine secara detail seperti yang ditunjukkan dalam interface konfigurasi modern.

## Fitur Utama

### Parameter Konfigurasi yang Didukung

1. **NUMA Offset** (0-7)
   - Offset node NUMA untuk alokasi memori
   - Berguna untuk sistem multi-processor

2. **Threads** (1-128)
   - Jumlah thread pencarian paralel
   - Sesuaikan dengan jumlah core CPU

3. **Hash** (1-32768 MB)
   - Ukuran hash table dalam MB
   - Hash lebih besar = performa lebih baik

4. **Table Memory** (1-8192 MB)
   - Ukuran cache tablebase dalam MB
   - Untuk Syzygy endgame tablebases

5. **MultiPV** (1-500)
   - Jumlah principal variations yang ditampilkan
   - Berguna untuk analisis mendalam

6. **Book File**
   - Path ke file opening book
   - Format yang didukung: .bin, .ctg, .abk

7. **Book Moves** (0-10000)
   - Maksimal gerakan yang digunakan dari opening book

8. **Hash File Name**
   - Path untuk persistent hash storage

9. **Log File**
   - Path file log engine untuk debugging

10. **Overhead ms** (0-5000)
    - Overhead waktu dalam milidetik
    - Kompensasi untuk latensi komunikasi

11. **Minimal Reporting** (0-10000)
    - Interval minimum antara laporan dalam ms

12. **Time Usage** (0-100)
    - Persentase penggunaan waktu yang dialokasikan

13. **SyzygyPath**
    - Path ke direktori Syzygy tablebase files

14. **Syzygy Probe Depth** (1-100)
    - Kedalaman minimum untuk probe tablebase

## Cara Menggunakan

### 1. Interface Konfigurasi Interaktif

```bash
# Compile configuration tool
make engine_config

# Jalankan interface konfigurasi
./engine_config
```

Interface ini menyediakan menu interaktif untuk:
- Melihat semua parameter
- Edit parameter individual
- Reset parameter ke default
- Muat/simpan konfigurasi dari/ke file
- Export ke format UCI

### 2. Command Line Interface

```bash
# Tampilkan bantuan
./engine_config --help

# Tampilkan konfigurasi saat ini
./engine_config --show

# Muat konfigurasi dari file
./engine_config --load config.ini

# Simpan konfigurasi ke file
./engine_config --save my_config.ini

# Export ke format UCI
./engine_config --uci

# Reset ke default
./engine_config --reset
```

### 3. Demo Interface

```bash
# Compile demo
make config_demo

# Jalankan demo dengan tampilan seperti gambar
./config_demo
```

Demo ini menampilkan interface yang mirip dengan gambar yang Anda berikan, dengan kotak-kotak parameter yang dapat diedit.

## Format File Konfigurasi

File konfigurasi menggunakan format INI sederhana:

```ini
# VibeChess Engine Configuration
# Generated automatically - modify with care

# Offset node NUMA untuk alokasi memori
NUMA Offset=0

# Jumlah thread pencarian paralel
Threads=1

# Ukuran hash table dalam MB
Hash=32

# Ukuran cache tablebase dalam MB
Table Memory=64

# Jumlah variasi utama yang ditampilkan
MultiPV=2

# Path ke file opening book
Book File=

# Maksimal gerakan dari opening book
Book Moves=1000

# Path file log engine
Log File=

# Overhead waktu dalam milidetik
Overhead ms=50

# Interval minimum antara laporan dalam ms
Minimal Reporting=0

# Persentase penggunaan waktu
Time Usage=0

# Path ke file Syzygy tablebase
SyzygyPath=

# Kedalaman minimum probe tablebase
Syzygy Probe Depth=1

# Enable null move pruning
NullMove=true

# Use built-in opening book
OwnBook=true

# Think on opponent's time
Ponder=false
```

## Integrasi dengan UCI Protocol

Sistem konfigurasi terintegrasi penuh dengan UCI Protocol. Semua parameter dapat diatur melalui perintah UCI `setoption`:

```
setoption name Hash value 128
setoption name Threads value 4
setoption name MultiPV value 3
```

## Tips Penggunaan

### Optimasi Performa

1. **Hash Table**: Gunakan sebanyak RAM yang tersedia (biasanya 25-50% dari total RAM)
2. **Threads**: Sesuaikan dengan jumlah core CPU (biasanya jumlah core - 1)
3. **MultiPV**: Gunakan 1 untuk permainan, 2-5 untuk analisis

### Opening Book

1. Download opening book berkualitas tinggi
2. Letakkan di direktori yang mudah diakses
3. Set `Book Moves` sesuai kebutuhan (1000-5000 untuk permainan normal)

### Syzygy Tablebases

1. Download tablebase dari situs resmi
2. Extract ke direktori khusus
3. Set `SyzygyPath` ke direktori tersebut
4. Set `Table Memory` minimal 64MB untuk performa optimal

## Troubleshooting

### File Tidak Ditemukan
- Pastikan path file benar dan dapat diakses
- Gunakan path absolut jika perlu

### Performa Lambat
- Kurangi ukuran Hash jika RAM terbatas
- Kurangi jumlah Threads jika CPU overload
- Periksa setting `Overhead ms`

### UCI Tidak Merespon
- Periksa format perintah UCI
- Pastikan parameter name sesuai dengan yang didukung

## Pengembangan Lebih Lanjut

Sistem ini dirancang untuk mudah diperluas. Untuk menambah parameter baru:

1. Tambahkan parameter di `EngineConfig::initializeDefaultParameters()`
2. Update `UCIProtocol::applyConfigToEngine()` untuk menerapkan setting
3. Implementasikan logika di `ChessEngine` class

## Lisensi

Bagian dari VibeChess Engine project.

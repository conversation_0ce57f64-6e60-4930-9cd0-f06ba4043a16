#include "EndgameTablebase.h"
#include <algorithm>
#include <cmath>
#include <cstdint>

EndgameTablebase::EndgameTablebase()
{
    // Initialize cache
    tablebaseCache_.reserve(10000);
}

bool EndgameTablebase::probe(const ChessBoard &board, Color sideToMove, TablebaseEntry &entry)
{
    if (!isKnownEndgame(board))
    {
        return false;
    }

    // Generate position hash
    uint64_t hash = 0; // Simplified hash - should use proper position hashing
    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            const Piece *piece = board.getPiece(Position(rank, file));
            if (piece)
            {
                hash ^= (uint64_t(piece->getType()) << (rank * 8 + file)) +
                        (uint64_t(piece->getColor()) << (rank * 8 + file + 32));
            }
        }
    }
    hash ^= (uint64_t(sideToMove) << 63);

    // Check cache first
    auto it = tablebaseCache_.find(hash);
    if (it != tablebaseCache_.end())
    {
        entry = it->second;
        return true;
    }

    // Evaluate the endgame
    std::string signature = getEndgameSignature(board);
    TablebaseEntry result;

    if (signature == "KPvK")
    {
        // Determine which side has the pawn
        std::vector<Position> whitePawns = findPieces(board, PieceType::PAWN, Color::WHITE);
        std::vector<Position> blackPawns = findPieces(board, PieceType::PAWN, Color::BLACK);

        if (!whitePawns.empty())
        {
            result = evaluateKPvK(board, Color::WHITE);
        }
        else if (!blackPawns.empty())
        {
            result = evaluateKPvK(board, Color::BLACK);
        }
    }
    else if (signature == "KQvK")
    {
        std::vector<Position> whiteQueens = findPieces(board, PieceType::QUEEN, Color::WHITE);
        std::vector<Position> blackQueens = findPieces(board, PieceType::QUEEN, Color::BLACK);

        if (!whiteQueens.empty())
        {
            result = evaluateKQvK(board, Color::WHITE);
        }
        else if (!blackQueens.empty())
        {
            result = evaluateKQvK(board, Color::BLACK);
        }
    }
    else if (signature == "KRvK")
    {
        std::vector<Position> whiteRooks = findPieces(board, PieceType::ROOK, Color::WHITE);
        std::vector<Position> blackRooks = findPieces(board, PieceType::ROOK, Color::BLACK);

        if (!whiteRooks.empty())
        {
            result = evaluateKRvK(board, Color::WHITE);
        }
        else if (!blackRooks.empty())
        {
            result = evaluateKRvK(board, Color::BLACK);
        }
    }

    // Cache the result
    if (result.result != Result::UNKNOWN)
    {
        tablebaseCache_[hash] = result;
        entry = result;
        return true;
    }

    return false;
}

bool EndgameTablebase::isTablebasePosition(const ChessBoard &board) const
{
    return isKnownEndgame(board);
}

bool EndgameTablebase::isKnownEndgame(const ChessBoard &board) const
{
    int pieceCount = 0;
    int pawnCount = 0;
    int whiteQueens = 0, blackQueens = 0;
    int whiteRooks = 0, blackRooks = 0;
    int whiteBishops = 0, blackBishops = 0;
    int whiteKnights = 0, blackKnights = 0;
    int whitePawns = 0, blackPawns = 0;

    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            const Piece *piece = board.getPiece(Position(rank, file));
            if (piece)
            {
                pieceCount++;
                switch (piece->getType())
                {
                case PieceType::PAWN:
                    pawnCount++;
                    if (piece->getColor() == Color::WHITE)
                        whitePawns++;
                    else
                        blackPawns++;
                    break;
                case PieceType::QUEEN:
                    if (piece->getColor() == Color::WHITE)
                        whiteQueens++;
                    else
                        blackQueens++;
                    break;
                case PieceType::ROOK:
                    if (piece->getColor() == Color::WHITE)
                        whiteRooks++;
                    else
                        blackRooks++;
                    break;
                case PieceType::BISHOP:
                    if (piece->getColor() == Color::WHITE)
                        whiteBishops++;
                    else
                        blackBishops++;
                    break;
                case PieceType::KNIGHT:
                    if (piece->getColor() == Color::WHITE)
                        whiteKnights++;
                    else
                        blackKnights++;
                    break;
                case PieceType::KING:
                    // Kings are always present
                    break;
                }
            }
        }
    }

    // Support basic endgames with few pieces
    if (pieceCount <= 5)
    {
        // KPvK
        if (pieceCount == 3 && pawnCount == 1)
            return true;

        // KQvK, KRvK
        if (pieceCount == 3 && (whiteQueens + blackQueens == 1 || whiteRooks + blackRooks == 1))
            return true;

        // KBBvK, KBNvK
        if (pieceCount == 4 && ((whiteBishops == 2 && blackBishops == 0) ||
                                (blackBishops == 2 && whiteBishops == 0) ||
                                (whiteBishops == 1 && whiteKnights == 1 && blackBishops == 0 && blackKnights == 0) ||
                                (blackBishops == 1 && blackKnights == 1 && whiteBishops == 0 && whiteKnights == 0)))
            return true;
    }

    return false;
}

std::string EndgameTablebase::getEndgameSignature(const ChessBoard &board) const
{
    std::vector<std::string> whitePieces, blackPieces;

    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            const Piece *piece = board.getPiece(Position(rank, file));
            if (piece && piece->getType() != PieceType::KING)
            {
                std::string pieceStr;
                switch (piece->getType())
                {
                case PieceType::PAWN:
                    pieceStr = "P";
                    break;
                case PieceType::QUEEN:
                    pieceStr = "Q";
                    break;
                case PieceType::ROOK:
                    pieceStr = "R";
                    break;
                case PieceType::BISHOP:
                    pieceStr = "B";
                    break;
                case PieceType::KNIGHT:
                    pieceStr = "N";
                    break;
                default:
                    break;
                }

                if (piece->getColor() == Color::WHITE)
                {
                    whitePieces.push_back(pieceStr);
                }
                else
                {
                    blackPieces.push_back(pieceStr);
                }
            }
        }
    }

    // Sort pieces
    std::sort(whitePieces.begin(), whitePieces.end());
    std::sort(blackPieces.begin(), blackPieces.end());

    // Create signature
    std::string signature = "K";
    for (const auto &piece : whitePieces)
        signature += piece;
    signature += "vK";
    for (const auto &piece : blackPieces)
        signature += piece;

    return signature;
}

Position EndgameTablebase::findKing(const ChessBoard &board, Color color) const
{
    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            Position pos(rank, file);
            const Piece *piece = board.getPiece(pos);
            if (piece && piece->getType() == PieceType::KING && piece->getColor() == color)
            {
                return pos;
            }
        }
    }
    return Position(-1, -1); // Invalid position
}

std::vector<Position> EndgameTablebase::findPieces(const ChessBoard &board, PieceType type, Color color) const
{
    std::vector<Position> pieces;
    for (int rank = 0; rank < 8; ++rank)
    {
        for (int file = 0; file < 8; ++file)
        {
            Position pos(rank, file);
            const Piece *piece = board.getPiece(pos);
            if (piece && piece->getType() == type && piece->getColor() == color)
            {
                pieces.push_back(pos);
            }
        }
    }
    return pieces;
}

int EndgameTablebase::manhattanDistance(const Position &a, const Position &b) const
{
    return std::abs(a.rank - b.rank) + std::abs(a.file - b.file);
}

int EndgameTablebase::chebyshevDistance(const Position &a, const Position &b) const
{
    return std::max(std::abs(a.rank - b.rank), std::abs(a.file - b.file));
}

int EndgameTablebase::resultToScore(const TablebaseEntry &entry, Color perspective) const
{
    switch (entry.result)
    {
    case Result::WIN:
        return 90000 - entry.distanceToMate; // Prefer shorter mates
    case Result::LOSS:
        return -90000 + entry.distanceToMate; // Prefer longer defenses
    case Result::DRAW:
        return 0;
    case Result::UNKNOWN:
    default:
        return 0;
    }
}

// Basic endgame evaluations
EndgameTablebase::TablebaseEntry EndgameTablebase::evaluateKPvK(const ChessBoard &board, Color strongSide) const
{
    Position strongKing = findKing(board, strongSide);
    Position weakKing = findKing(board, oppositeColor(strongSide));
    std::vector<Position> pawns = findPieces(board, PieceType::PAWN, strongSide);

    if (pawns.empty() || !strongKing.isValid() || !weakKing.isValid())
    {
        return TablebaseEntry(Result::UNKNOWN, -1);
    }

    Position pawn = pawns[0];

    // Basic KPvK evaluation
    int pawnRank = (strongSide == Color::WHITE) ? pawn.rank : (7 - pawn.rank);
    int promotionRank = (strongSide == Color::WHITE) ? 7 : 0;

    // Distance to promotion
    int distanceToPromotion = std::abs(promotionRank - pawn.rank);

    // King distances
    int strongKingToPawn = chebyshevDistance(strongKing, pawn);
    int weakKingToPawn = chebyshevDistance(weakKing, pawn);

    // Simple rule: if pawn is far advanced and supported by king, it's winning
    if (pawnRank >= 5 && strongKingToPawn <= 2 && weakKingToPawn > strongKingToPawn + 1)
    {
        return TablebaseEntry(Result::WIN, distanceToPromotion + 5);
    }

    // If pawn is blocked and king can't help, it might be draw
    if (weakKingToPawn <= strongKingToPawn && pawnRank < 4)
    {
        return TablebaseEntry(Result::DRAW, -1);
    }

    // Default to winning but with longer mate distance
    return TablebaseEntry(Result::WIN, distanceToPromotion + 10);
}

EndgameTablebase::TablebaseEntry EndgameTablebase::evaluateKQvK(const ChessBoard &board, Color strongSide) const
{
    Position strongKing = findKing(board, strongSide);
    Position weakKing = findKing(board, oppositeColor(strongSide));
    std::vector<Position> queens = findPieces(board, PieceType::QUEEN, strongSide);

    if (queens.empty() || !strongKing.isValid() || !weakKing.isValid())
    {
        return TablebaseEntry(Result::UNKNOWN, -1);
    }

    Position queen = queens[0];

    // KQvK is always winning
    // Distance to mate depends on how close the weak king is to the edge
    int edgeDistance = std::min({weakKing.rank, weakKing.file, 7 - weakKing.rank, 7 - weakKing.file});
    int mateDistance = edgeDistance + chebyshevDistance(strongKing, weakKing) / 2;

    return TablebaseEntry(Result::WIN, std::max(1, mateDistance));
}

EndgameTablebase::TablebaseEntry EndgameTablebase::evaluateKRvK(const ChessBoard &board, Color strongSide) const
{
    Position strongKing = findKing(board, strongSide);
    Position weakKing = findKing(board, oppositeColor(strongSide));
    std::vector<Position> rooks = findPieces(board, PieceType::ROOK, strongSide);

    if (rooks.empty() || !strongKing.isValid() || !weakKing.isValid())
    {
        return TablebaseEntry(Result::UNKNOWN, -1);
    }

    Position rook = rooks[0];

    // KRvK is always winning
    // Similar to KQvK but takes longer
    int edgeDistance = std::min({weakKing.rank, weakKing.file, 7 - weakKing.rank, 7 - weakKing.file});
    int mateDistance = edgeDistance + chebyshevDistance(strongKing, weakKing) / 2 + 5;

    return TablebaseEntry(Result::WIN, std::max(1, mateDistance));
}

EndgameTablebase::TablebaseEntry EndgameTablebase::evaluateKBBvK(const ChessBoard &board, Color strongSide) const
{
    Position strongKing = findKing(board, strongSide);
    Position weakKing = findKing(board, oppositeColor(strongSide));
    std::vector<Position> bishops = findPieces(board, PieceType::BISHOP, strongSide);

    if (bishops.size() != 2 || !strongKing.isValid() || !weakKing.isValid())
    {
        return TablebaseEntry(Result::UNKNOWN, -1);
    }

    // KBBvK is winning - force king to corner
    int cornerDistance = std::min({chebyshevDistance(weakKing, Position(0, 0)),
                                   chebyshevDistance(weakKing, Position(0, 7)),
                                   chebyshevDistance(weakKing, Position(7, 0)),
                                   chebyshevDistance(weakKing, Position(7, 7))});

    int mateDistance = cornerDistance + chebyshevDistance(strongKing, weakKing) / 2 + 10;

    return TablebaseEntry(Result::WIN, std::max(1, mateDistance));
}

EndgameTablebase::TablebaseEntry EndgameTablebase::evaluateKBNvK(const ChessBoard &board, Color strongSide) const
{
    Position strongKing = findKing(board, strongSide);
    Position weakKing = findKing(board, oppositeColor(strongSide));
    std::vector<Position> bishops = findPieces(board, PieceType::BISHOP, strongSide);
    std::vector<Position> knights = findPieces(board, PieceType::KNIGHT, strongSide);

    if (bishops.size() != 1 || knights.size() != 1 || !strongKing.isValid() || !weakKing.isValid())
    {
        return TablebaseEntry(Result::UNKNOWN, -1);
    }

    // KBNvK is winning but complex - force to correct corner
    Position bishop = bishops[0];

    // Determine bishop color (light or dark squares)
    bool lightSquareBishop = ((bishop.rank + bishop.file) % 2 == 0);

    // Force to correct corner based on bishop color
    Position targetCorner;
    if (lightSquareBishop)
    {
        targetCorner = Position(0, 0); // or (7,7)
    }
    else
    {
        targetCorner = Position(0, 7); // or (7,0)
    }

    int cornerDistance = chebyshevDistance(weakKing, targetCorner);
    int mateDistance = cornerDistance + chebyshevDistance(strongKing, weakKing) / 2 + 15;

    return TablebaseEntry(Result::WIN, std::max(1, mateDistance));
}
